<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Next 18hrs - Task Organizer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f7;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .left-panel {
            width: 50%;
            background: white;
            border-right: 1px solid #e5e5e7;
            display: flex;
            flex-direction: column;
        }

        .right-panel {
            width: 50%;
            background: #fafafa;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            padding: 20px;
            border-bottom: 1px solid #e5e5e7;
            background: #f8f9fa;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
        }

        .panel-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .task-item {
            background: white;
            border: 1px solid #e5e5e7;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
        }

        .task-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid #007aff;
            border-radius: 4px;
            cursor: pointer;
        }

        .task-checkbox.checked {
            background: #007aff;
            position: relative;
        }

        .task-checkbox.checked::after {
            content: '✓';
            color: white;
            position: absolute;
            top: -2px;
            left: 3px;
            font-size: 14px;
        }

        .task-content {
            flex: 1;
        }

        .task-description {
            font-size: 14px;
            color: #1d1d1f;
            margin-bottom: 4px;
        }

        .task-meta {
            font-size: 12px;
            color: #86868b;
        }

        .conversation-entry {
            margin-bottom: 16px;
            padding: 12px;
            border-radius: 8px;
        }

        .conversation-entry.user {
            background: #007aff;
            color: white;
            margin-left: 20px;
        }

        .conversation-entry.ai {
            background: #e5e5e7;
            color: #1d1d1f;
            margin-right: 20px;
        }

        .conversation-entry.system {
            background: #f0f0f0;
            color: #666;
            font-style: italic;
            font-size: 12px;
        }

        .entry-sender {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .entry-content {
            line-height: 1.4;
        }

        .entry-timestamp {
            font-size: 11px;
            opacity: 0.7;
            margin-top: 4px;
        }

        .input-area {
            padding: 20px;
            border-top: 1px solid #e5e5e7;
            background: white;
        }

        .input-group {
            display: flex;
            gap: 12px;
        }

        .text-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #d2d2d7;
            border-radius: 8px;
            font-size: 14px;
            outline: none;
        }

        .text-input:focus {
            border-color: #007aff;
        }

        .btn {
            padding: 12px 24px;
            background: #007aff;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #0056b3;
        }

        .btn:disabled {
            background: #d2d2d7;
            cursor: not-allowed;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active {
            background: #30d158;
        }

        .status-processing {
            background: #ff9500;
        }

        .empty-state {
            text-align: center;
            color: #86868b;
            padding: 40px 20px;
        }

        .empty-state-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .btn-delete {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            border: none;
            background: #ff3b30;
            color: white;
            border-radius: 50%;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0.7;
            transition: opacity 0.2s;
        }

        .btn-delete:hover {
            opacity: 1;
        }

        .task-item:hover .btn-delete {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Left Panel: Tasks -->
        <div class="left-panel">
            <div class="panel-header">
                <div class="panel-title">🎯 Next 18hrs Signal</div>
            </div>
            <div class="panel-content" id="tasks-container">
                <div class="empty-state">
                    <div class="empty-state-icon">📋</div>
                    <div>No tasks yet. Start a conversation to generate tasks!</div>
                </div>
            </div>
        </div>

        <!-- Right Panel: Conversation -->
        <div class="right-panel">
            <div class="panel-header">
                <div class="panel-title">🗨️ Conversation</div>
            </div>
            <div class="panel-content" id="conversation-container">
                <div class="empty-state">
                    <div class="empty-state-icon">💬</div>
                    <div>Start by typing your thoughts below...</div>
                </div>
            </div>
            <div class="input-area">
                <div class="input-group">
                    <input type="text" class="text-input" id="message-input" placeholder="Dump your thoughts here..." />
                    <button class="btn" id="send-btn">Send</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global state
        let tasks = [];
        let conversation = [];
        let isLoading = false;

        // DOM elements
        const tasksContainer = document.getElementById('tasks-container');
        const conversationContainer = document.getElementById('conversation-container');
        const messageInput = document.getElementById('message-input');
        const sendBtn = document.getElementById('send-btn');

        // API functions
        async function fetchState() {
            try {
                const response = await fetch('/api/state');
                const data = await response.json();
                
                if (data.error) {
                    console.error('Error fetching state:', data.error);
                    return;
                }
                
                tasks = data.tasks || [];
                conversation = data.conversation || [];
                
                renderTasks();
                renderConversation();
            } catch (error) {
                console.error('Error fetching state:', error);
                showError('Failed to load data from server');
            }
        }

        async function sendMessage(message) {
            if (isLoading) return;
            
            isLoading = true;
            sendBtn.disabled = true;
            sendBtn.textContent = 'Processing...';
            
            try {
                const response = await fetch('/api/dump', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message })
                });
                
                const data = await response.json();
                
                if (data.error) {
                    showError(data.error);
                    return;
                }
                
                // Update state with response
                tasks = data.tasks || [];
                conversation = data.conversation || [];
                
                renderTasks();
                renderConversation();
                
                // Show success message if provided
                if (data.message) {
                    console.log('AI Response:', data.message);
                }
                
            } catch (error) {
                console.error('Error sending message:', error);
                showError('Failed to send message');
            } finally {
                isLoading = false;
                sendBtn.disabled = false;
                sendBtn.textContent = 'Send';
            }
        }

        async function toggleTask(taskId) {
            try {
                const response = await fetch(`/api/tasks/${taskId}/toggle`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.error) {
                    showError(data.error);
                    return;
                }
                
                tasks = data.tasks || [];
                conversation = data.conversation || [];
                
                renderTasks();
                renderConversation();
                
            } catch (error) {
                console.error('Error toggling task:', error);
                showError('Failed to update task');
            }
        }

        async function deleteTask(taskId) {
            if (!confirm('Are you sure you want to delete this task? This means you\'re ignoring/cancelling it.')) {
                return;
            }
            
            try {
                const response = await fetch(`/api/tasks/${taskId}/delete`, {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.error) {
                    showError(data.error);
                    return;
                }
                
                tasks = data.tasks || [];
                conversation = data.conversation || [];
                
                renderTasks();
                renderConversation();
                
                if (data.message) {
                    console.log('Task deleted:', data.message);
                }
                
            } catch (error) {
                console.error('Error deleting task:', error);
                showError('Failed to delete task');
            }
        }

        function showError(message) {
            // Simple error display - could be enhanced with a proper notification system
            alert('Error: ' + message);
        }

        // Initialize with real data from backend
        async function init() {
            await fetchState();
        }

        function renderTasks() {
            if (tasks.length === 0) {
                tasksContainer.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">📋</div>
                        <div>No tasks yet. Start a conversation to generate tasks!</div>
                    </div>
                `;
                return;
            }

            const tasksHtml = tasks.map(task => `
                <div class="task-item">
                    <div class="task-checkbox ${task.is_completed ? 'checked' : ''}" 
                         onclick="toggleTask('${task.id}')"></div>
                    <div class="task-content">
                        <div class="task-description">${task.description}</div>
                        <div class="task-meta">Priority ${task.priority} • ${new Date(task.created_at).toLocaleDateString()}</div>
                    </div>
                    <button class="btn-delete" onclick="deleteTask('${task.id}')" title="Delete task (ignore/cancel)">×</button>
                </div>
            `).join('');

            tasksContainer.innerHTML = tasksHtml;
        }

        function renderConversation() {
            if (conversation.length === 0) {
                conversationContainer.innerHTML = `
                    <div class="empty-state">
                        <div class="empty-state-icon">💬</div>
                        <div>Start by typing your thoughts below...</div>
                    </div>
                `;
                return;
            }

            const conversationHtml = conversation.map(entry => `
                <div class="conversation-entry ${entry.sender}">
                    <div class="entry-sender">
                        ${entry.sender === 'user' ? '👤 You' : 
                          entry.sender === 'ai' ? '🤖 AI' : 
                          '⚙️ System'}
                    </div>
                    <div class="entry-content">${entry.content.replace(/\n/g, '<br>')}</div>
                    <div class="entry-timestamp">${new Date(entry.timestamp).toLocaleTimeString()}</div>
                </div>
            `).join('');

            conversationContainer.innerHTML = conversationHtml;
            conversationContainer.scrollTop = conversationContainer.scrollHeight;
        }

        // This function is now handled by the async toggleTask above

        function addMessage() {
            const message = messageInput.value.trim();
            if (!message || isLoading) return;

            messageInput.value = '';
            sendMessage(message);
        }

        // Event listeners
        sendBtn.addEventListener('click', addMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                addMessage();
            }
        });

        // Initialize with real data
        init();
    </script>
</body>
</html>
