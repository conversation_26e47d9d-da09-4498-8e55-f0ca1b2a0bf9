

Field Notes: Karachi – Systems Drifting Out of Sync

Karachi had settled into that sluggish monsoon lull again—humid, slightly dusty, not quite hot enough to explain the discomfort but still somehow draining. I hadn’t planned for the day to turn into a full inventory of failures, but as usual, reality had other plans. What started as a quick check-in on the AC situation spiraled into a broader audit—across rooms, wiring, appliances, systems, and screens.

The upstairs rooms were the first red flag. Both air conditioning units had quit—not with a bang, but with a quiet betrayal. No airflow, no cold hum, just the stillness of a machine that had decided to give up. It might’ve gone unnoticed if the drawing room unit hadn’t joined them. Three dead zones, all at once. I remembered we had salvaged a card from one of the older traded units. It’s been sitting on the shelf like a question mark ever since. Whether it could breathe life back into one of the dead units remained to be seen. It was an open possibility, untested and waiting.

That same hallway—where the air used to carry a steady breeze—now offered something more unpredictable. The pedestal fan, once reliable, had begun delivering electric shocks. Subtle enough to dismiss the first time, but the second one came with a spark. I unplugged it immediately. It’s strange how quickly something can shift from comfort to hazard.

The garden wasn’t spared either. By dusk, one of the walkway lights failed to flicker on, leaving a section of the perimeter shrouded. Not catastrophic—until you remember that the cameras had started failing too. The NVR had gone off its script; footage wasn’t being logged, IP addresses seemed out of sync. One of the outdoor units had a cracked lens, or maybe it was internal damage. Either way, it had gone dark—just like the garden corner it used to watch.

Elsewhere, things were fraying too. The car, for instance. I hadn’t noticed it immediately, but the ride to the corner store felt looser than usual—more sway in the steering, more vibration through the pedals. A quick inspection revealed that all four tires were past their useful life. One had visible cracking. Not the kind of thing you want to risk, even on short drives.

The inverter system wasn’t exactly failing, but it had entered that subtle in-between state. Charge-holding seemed weak. The auto-switch lagged. We’ve had a few outages this week and it didn’t respond with its usual confidence. It felt like the kind of problem you don’t deal with—until you really wish you had.

Meanwhile, a separate thread had begun to fray on the administrative front. The Malaysia visa and travel-related documents were still pending in various stages. The FBI fingerprint submission was hanging in limbo. All the forms were filled, and everything was ready to go—but the actual prints had yet to be sent. That meant both the Malaysia visa and Global Entry were blocked from moving forward.

The Social Security card replacement was also unresolved. It’s not something that can be completed here—will need to wait till I’m physically back in the U.S. As for the Naturalization certificate, the notification had come through, but I hadn’t confirmed whether any further steps were needed. It was another thread left dangling, half-tied.

And then, the MDEC portal. The one gateway holding together the visa strategy for Malaysia. Jumeidi had finally received the first password and was preparing to reset the credentials. He was mid-transit to Malta—on a plane, somewhere over Europe—planning to handle it a few hours after arrival. Once logged in, he’d reset the access, send over the new credentials, and from there, the lawyer would finally be able to log in and assess our status.

Vortech’s MD registration had been confirmed as still active, but the real question was whether the visa readiness was still intact. We’d need Shah to coordinate with MDEC’s client relations team to make sense of that backend. Contact details were ready. I just hadn’t passed them along yet.

So that was the week—nothing dramatic, but enough small fractures across enough systems that you start to feel like the architecture itself is creaking. Cooling, lighting, mobility, surveillance, energy, identity, compliance—it was all one interconnected fabric, and each piece was quietly whispering: you’re overdue.

I stood at the front door that evening, taking stock of what still worked. The lock clicked fine. The hinges were smooth. Small wins. But the systems—those invisible layers underneath—had started blinking yellow. It was time to triage. Quietly. Methodically. Before the reds start showing up.
