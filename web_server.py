#!/usr/bin/env python3
"""
Web server for Next 18hrs Task Organizer
Provides a REST API to the existing Python backend
"""

import json
import os
import sys
from datetime import datetime
from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
from next18hrs_poc import Next18HrsPOC

app = Flask(__name__)
CORS(app)  # Enable CORS for local development

# Initialize the Next18Hrs engine
engine = Next18HrsPOC()

@app.route('/')
def index():
    """Serve the main HTML page"""
    return send_from_directory('.', 'index.html')

@app.route('/api/state')
def get_state():
    """Get current state (tasks and conversation)"""
    try:
        # Get active conversation
        conv = engine.get_active_conversation()
        if not conv:
            return jsonify({
                'tasks': [],
                'conversation': [],
                'message': 'No active conversation'
            })
        
        # Get tasks
        tasks = list(engine.tasks.values())
        
        # Get conversation entries
        conversation = conv.get('entries', [])
        
        return jsonify({
            'tasks': tasks,
            'conversation': conversation,
            'active_conversation_id': engine.active_conversation_id
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/dump', methods=['POST'])
def dump_message():
    """Process a new message through the AI"""
    try:
        data = request.get_json()
        message = data.get('message', '').strip()
        
        if not message:
            return jsonify({'error': 'Message is required'}), 400
        
        # Add user input to conversation
        user_entry = engine.add_user_input(message)
        
        # Process with AI
        result = engine.process_with_ai(message)
        
        if 'error' in result:
            return jsonify({'error': result['error']}), 500
        
        # Save state
        engine.save_state()
        
        # Return updated state
        conv = engine.get_active_conversation()
        tasks = list(engine.tasks.values())
        conversation = conv.get('entries', []) if conv else []
        
        return jsonify({
            'success': True,
            'ai_response': result.get('ai_response', ''),
            'tasks': tasks,
            'conversation': conversation,
            'message': result.get('message', '')
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/tasks/<task_id>/complete', methods=['POST'])
def complete_task(task_id):
    """Mark a task as completed"""
    try:
        success = engine.complete_task(task_id)
        
        if success:
            # Save state
            engine.save_state()
            
            # Return updated state
            conv = engine.get_active_conversation()
            tasks = list(engine.tasks.values())
            conversation = conv.get('entries', []) if conv else []
            
            return jsonify({
                'success': True,
                'tasks': tasks,
                'conversation': conversation
            })
        else:
            return jsonify({'error': 'Task not found'}), 404
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/tasks/<task_id>/toggle', methods=['POST'])
def toggle_task(task_id):
    """Toggle task completion status"""
    try:
        if task_id not in engine.tasks:
            return jsonify({'error': 'Task not found'}), 404
        
        task = engine.tasks[task_id]
        task['is_completed'] = not task['is_completed']
        task['updated_at'] = datetime.now().isoformat()
        
        # Log the action in conversation
        engine.add_system_log(f"Task {'completed' if task['is_completed'] else 'uncompleted'}: '{task['description']}'")
        
        # Save state
        engine.save_state()
        
        # Return updated state
        conv = engine.get_active_conversation()
        tasks = list(engine.tasks.values())
        conversation = conv.get('entries', []) if conv else []
        
        return jsonify({
            'success': True,
            'tasks': tasks,
            'conversation': conversation
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/tasks/<task_id>/delete', methods=['POST'])
def delete_task(task_id):
    """Delete a task (mark as ignored/cancelled)"""
    try:
        if task_id not in engine.tasks:
            return jsonify({'error': 'Task not found'}), 404
        
        task_description = engine.tasks[task_id]['description']
        
        # Remove task from state
        del engine.tasks[task_id]
        
        # Log the deletion in conversation
        engine.add_system_log(f"Task deleted (ignored/cancelled): '{task_description}'")
        
        # Save state
        engine.save_state()
        
        # Return updated state
        conv = engine.get_active_conversation()
        tasks = list(engine.tasks.values())
        conversation = conv.get('entries', []) if conv else []
        
        return jsonify({
            'success': True,
            'tasks': tasks,
            'conversation': conversation,
            'message': f'Task deleted: {task_description}'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/bank', methods=['POST'])
def bank_conversation():
    """Manually trigger conversation banking"""
    try:
        if not engine.active_conversation_id:
            return jsonify({'error': 'No active conversation'}), 400
        
        summary = engine.bank_conversation(engine.active_conversation_id)
        
        if summary:
            # Save state
            engine.save_state()
            
            # Return updated state
            conv = engine.get_active_conversation()
            tasks = list(engine.tasks.values())
            conversation = conv.get('entries', []) if conv else []
            
            return jsonify({
                'success': True,
                'summary': summary,
                'tasks': tasks,
                'conversation': conversation,
                'message': 'Conversation banked successfully'
            })
        else:
            return jsonify({'error': 'Banking failed or not needed'}), 400
            
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/new-conversation', methods=['POST'])
def new_conversation():
    """Start a new conversation"""
    try:
        data = request.get_json()
        title = data.get('title', 'New Conversation')
        
        conv_id = engine.create_conversation(title)
        
        # Save state
        engine.save_state()
        
        return jsonify({
            'success': True,
            'conversation_id': conv_id,
            'message': f'New conversation started: {title}'
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🚀 Starting Next 18hrs Web Server...")
    print("📱 Open http://localhost:8080 in your browser")
    print("🛑 Press Ctrl+C to stop")
    
    # Check if OpenAI is configured
    if not engine.model:
        print("⚠️  Warning: OpenAI not configured. AI features will not work.")
        print("   Please set OPENAI_API_KEY in your .env file")
    
    app.run(debug=True, host='0.0.0.0', port=8080)
