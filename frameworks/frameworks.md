# Productivity Frameworks Collection

This directory contains individual framework prompts that can be used as alternative system prompts for the Next 18hrs task organizer.

## 🎯 Available Frameworks

### **Daily Focus Frameworks**
- **`signal.txt`** - Signal vs. Noise (<PERSON>) - Focus on top 3-5 critical tasks
- **`mits.txt`** - Most Important Tasks - Pick 1-3 daily priorities
- **`pomodoro.txt`** - Pomodoro Technique - 25 min focus + 5 min rest cycles
- **`timeblocking.txt`** - Time Blocking (Deep Work) - Schedule protected focus time

### **Prioritization Frameworks**
- **`eisenhower.txt`** - <PERSON> - <PERSON> vs. Important categorization
- **`pareto.txt`** - Pareto Principle (80/20) - Focus on high-leverage activities
- **`onething.txt`** - The ONE Thing - Single most impactful action
- **`essentialism.txt`** - Essentialism - "Less, but better" approach

### **System Frameworks**
- **`gtd.txt`** - Getting Things Done - Complete task management system
- **`okrs.txt`** - OKRs - Objectives and Key Results goal setting
- **`kanban.txt`** - Kanban - Visual workflow management
- **`agile.txt`** - Agile/Scrum - Iterative development approach

### **Purpose & Organization Frameworks**
- **`ikigai.txt`** - Ikigai - Purpose and meaning framework
- **`para.txt`** - PARA - Digital organization system

## 🚀 Usage

### **Switching Frameworks**
To use a different framework with the Next 18hrs POC:

1. **Copy the desired framework** to replace the system prompt:
   ```bash
   cp frameworks/[framework].txt llm_system_prompt.txt
   ```

2. **Run the POC** with the new framework:
   ```bash
   python next18hrs_poc.py dump_file simple_dump.txt
   ```

### **Framework Comparison**
Each framework provides a unique perspective on:
- **Task prioritization** - How to choose what to work on
- **Time management** - How to structure your work
- **Focus strategies** - How to maintain concentration
- **Goal alignment** - How to connect daily work to larger objectives

### **Choosing a Framework**
- **Need laser focus?** → Signal vs. Noise, MITs, The ONE Thing
- **Overwhelmed by tasks?** → GTD, Eisenhower Matrix, Kanban
- **Want strategic alignment?** → OKRs, Ikigai, Essentialism
- **Need time management?** → Time Blocking, Pomodoro, Agile
- **Want better organization?** → PARA, Kanban, GTD

## 📊 Framework Characteristics

| Framework | Best For | Complexity | Time Horizon |
|-----------|----------|------------|--------------|
| Signal vs. Noise | Daily focus | Low | 18 hours |
| MITs | Daily priorities | Low | 1 day |
| Pomodoro | Focus sessions | Low | 25 minutes |
| Time Blocking | Deep work | Medium | 1 day |
| Eisenhower | Task sorting | Low | 1 week |
| Pareto | Strategic focus | Medium | 1 month |
| The ONE Thing | Singular focus | Low | 1 day |
| Essentialism | Life design | High | 1 year |
| GTD | Complete system | High | Ongoing |
| OKRs | Goal setting | Medium | 1 quarter |
| Kanban | Workflow | Medium | Ongoing |
| Agile | Project work | High | 2-4 weeks |
| Ikigai | Purpose | Medium | Life |
| PARA | Organization | Medium | Ongoing |

## 🔄 Framework Stacking
These frameworks can be used together:
- **Daily:** Signal/MITs + Pomodoro + Time Blocking
- **Weekly:** Eisenhower + GTD + Kanban
- **Monthly:** Pareto + The ONE Thing + Essentialism
- **Quarterly:** OKRs + Agile + Ikigai
- **Ongoing:** PARA + GTD + Kanban

Each framework adds value to a different time horizon, creating a complete productivity system.

---

## 📄 Original Framework Comparison

| Framework | Core Idea | Strengths | Weaknesses | Best Use Case |
|-----------|-----------|-----------|------------|---------------|
| **Signal vs. Noise (Steve Jobs)** | Focus only on the top 3–5 critical tasks; cut distractions. | Extreme clarity, ruthless elimination, keeps priorities sharp. | Can neglect longer-term but non-urgent tasks; harsh if over-applied. | When you need *laser focus* on a few vital outcomes (e.g., product launch, crisis). |
| **Eisenhower Matrix** | Categorize by *Urgent vs. Important*. | Helps fight "firefighting" mindset; easy to visualize. | Can still create big lists; deciding "important" can be subjective. | When overloaded by tasks and need quick sorting. |
| **Getting Things Done (GTD)** | Capture everything → Clarify → Organize → Reflect → Act. | Reduces mental clutter; great for managing complexity. | Can feel process-heavy; risks "busy work." | Knowledge workers juggling many inputs (emails, projects, requests). |
| **Pareto Principle (80/20)** | 20% of effort produces 80% of results. | Simple, high-leverage focus; universal applicability. | Doesn't tell you *which* 20% without analysis. | Strategic prioritization (e.g., which clients, features, or habits to focus on). |
| **OKRs** | Ambitious **Objectives** with measurable **Key Results**. | Aligns teams, drives measurable progress. | Needs discipline; can encourage chasing numbers over substance. | Team or company-wide alignment (Google, startups, projects). |
| **Time Blocking (Deep Work)** | Schedule deep work blocks, guard focus. | Protects attention, builds consistency. | Rigid; hard in reactive jobs. | Creators, coders, researchers needing deep concentration. |
| **Pomodoro** | 25 min work, 5 min break. | Boosts momentum, reduces procrastination. | Interrupts flow; not suited for long creative work. | Overcoming procrastination, repetitive tasks, study sessions. |
| **MITs (Most Important Tasks)** | Pick 1–3 tasks daily. | Minimalist, easy to adopt. | Too simple for complex workflows. | Daily focus without overplanning. |
| **The ONE Thing** | Find the ONE task that makes everything else easier. | Ultra-simplifying; high leverage. | Risk of ignoring secondary but necessary work. | Long-term prioritization, simplifying big goals. |
| **Essentialism** | "Less, but better." Say no to almost everything. | Creates space for quality; aligns with purpose. | Ruthless cuts can strain relationships or commitments. | Life design, career focus, burnout recovery. |
| **Ikigai** | Align work with passion, skills, need, and reward. | Purpose-driven, motivating. | Abstract; not tactical. | Career/life direction, values alignment. |
| **Agile / Scrum** | Iterative sprints, feedback, continuous improvement. | Team productivity, adaptability. | Can be overformalized; not for solo workers. | Software, product teams, startups. |
| **Kanban** | Visual task flow: To-Do → Doing → Done. | Intuitive, visual clarity. | Doesn't prioritize by impact. | Tracking project flow, team or personal boards. |
| **PARA (Tiago Forte)** | Organize into Projects, Areas, Resources, Archives. | Great for digital organization; scalable. | More about storage than prioritization. | Knowledge management, digital notes/files. |
