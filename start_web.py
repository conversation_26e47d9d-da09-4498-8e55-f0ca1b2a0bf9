#!/usr/bin/env python3
"""
Startup script for Next 18hrs Web Interface
Installs dependencies and starts the web server
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def check_env_file():
    """Check if .env file exists"""
    if not os.path.exists('.env'):
        print("⚠️  No .env file found. Creating template...")
        with open('.env', 'w') as f:
            f.write("# Next 18hrs Environment Variables\n")
            f.write("OPENAI_API_KEY=your-openai-api-key-here\n")
        print("📝 Please edit .env file and add your OpenAI API key")
        return False
    return True

def main():
    print("🚀 Next 18hrs Web Interface Startup")
    print("=" * 50)
    
    # Install dependencies
    if not install_requirements():
        sys.exit(1)
    
    # Check environment
    env_ok = check_env_file()
    
    print("\n🌐 Starting web server...")
    print("📱 Open http://localhost:8080 in your browser")
    print("🛑 Press Ctrl+C to stop")
    
    if not env_ok:
        print("\n⚠️  Warning: OpenAI API key not configured. AI features will not work.")
        print("   Please edit .env file and add your OpenAI API key")
    
    # Start the web server
    try:
        from web_server import app
        app.run(debug=True, host='0.0.0.0', port=8080)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
