# Next 18hrs Web Interface

A modern web interface for the Next 18hrs Task Organizer, featuring a split-view design with tasks on the left and conversation on the right.

## 🚀 Quick Start

### Option 1: Automated Setup
```bash
python start_web.py
```

### Option 2: Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Start web server
python web_server.py
```

Then open http://localhost:5000 in your browser.

## 🎯 Features

### **Split-View Interface**
- **Left Panel**: Task list with priority-based display
- **Right Panel**: Conversation thread with AI interactions
- **Real-time Updates**: Tasks and conversation sync automatically

### **Interactive Task Management**
- ✅ **Click to Complete**: Click task checkboxes to mark complete/incomplete
- 🎯 **Priority Display**: Tasks shown with priority levels
- 📅 **Timestamps**: Creation and update times displayed
- 🔄 **Auto-sync**: Changes immediately reflected in conversation

### **AI-Powered Conversation**
- 💬 **Natural Input**: Type thoughts freely in the input field
- 🤖 **LLM Processing**: OpenAI GPT-4o-mini processes and extracts tasks
- 📝 **Smart Consolidation**: Duplicate tasks automatically merged
- 🎯 **Signal Focus**: Only critical 18-hour tasks prioritized

### **Real-time Features**
- ⚡ **Live Updates**: No page refresh needed
- 🔄 **State Persistence**: All changes saved automatically
- 📊 **Progress Tracking**: Visual feedback during AI processing
- 🏦 **Auto-banking**: Long conversations automatically summarized

## 🛠️ Technical Architecture

### **Frontend (HTML/CSS/JavaScript)**
- **Modern Design**: Clean, Apple-inspired interface
- **Responsive Layout**: Works on desktop and mobile
- **Real-time Updates**: Fetch API for seamless backend communication
- **Error Handling**: User-friendly error messages

### **Backend (Python/Flask)**
- **REST API**: Clean endpoints for all operations
- **CORS Enabled**: Cross-origin requests supported
- **State Management**: Integrates with existing YAML persistence
- **LLM Integration**: Full OpenAI GPT-4o-mini support

### **API Endpoints**
- `GET /api/state` - Get current tasks and conversation
- `POST /api/dump` - Process new message through AI
- `POST /api/tasks/{id}/toggle` - Toggle task completion
- `POST /api/bank` - Manually trigger conversation banking
- `POST /api/new-conversation` - Start new conversation

## 🎨 User Interface

### **Left Panel: Tasks**
```
🎯 Next 18hrs Signal
┌─────────────────────────────────┐
│ ☐ Schedule AC repair for 3     │
│   non-working units            │
│   Priority 1 • 12/25/2024      │
├─────────────────────────────────┤
│ ☐ Replace dangerous pedestal   │
│   fan with electric shock      │
│   Priority 2 • 12/25/2024      │
└─────────────────────────────────┘
```

### **Right Panel: Conversation**
```
🗨️ Conversation
┌─────────────────────────────────┐
│ 👤 You                          │
│ I need to deal with all these   │
│ system failures at home...      │
│ 2:30 PM                         │
├─────────────────────────────────┤
│ 🤖 AI                           │
│ I understand you're dealing     │
│ with multiple system failures.  │
│ Let me help you prioritize...   │
│ 2:30 PM                         │
└─────────────────────────────────┘
[Type your thoughts here...] [Send]
```

## 🔧 Configuration

### **Environment Setup**
Create a `.env` file:
```bash
OPENAI_API_KEY=your-openai-api-key-here
```

### **Customization**
- **Frameworks**: Switch system prompts in `frameworks/` directory
- **Styling**: Modify CSS in `index.html` for custom themes
- **API**: Extend `web_server.py` for additional endpoints

## 🚀 Usage Examples

### **Basic Workflow**
1. **Dump**: Type "I need to fix the AC, replace tires, and handle visa paperwork"
2. **Digest**: AI processes and consolidates into priority tasks
3. **Decide**: Review generated tasks in left panel
4. **Do**: Click checkboxes to complete tasks as you work

### **Advanced Features**
- **Banking**: Long conversations automatically summarized
- **Persistence**: All data saved to `next18hrs_state.yaml`
- **Debugging**: Check `active_prompt.txt` and `active_response.txt` for LLM details

## 🐛 Troubleshooting

### **Common Issues**
- **"OpenAI not configured"**: Add API key to `.env` file
- **"Failed to load data"**: Check if `next18hrs_state.yaml` exists
- **"CORS error"**: Ensure Flask-CORS is installed

### **Debug Mode**
- Check browser console for JavaScript errors
- Check terminal for Python errors
- Review `active_prompt.txt` for LLM input
- Review `active_response.txt` for LLM output

## 🔄 Integration with CLI

The web interface uses the same backend as the CLI version:
- **Shared State**: Both use `next18hrs_state.yaml`
- **Same AI**: Identical LLM processing
- **Consistent Data**: Changes in web reflect in CLI and vice versa

## 🎯 Next Steps

1. **Test the Interface**: Try the complete Dump → Digest → Decide → Do workflow
2. **Customize Styling**: Modify CSS for your preferred look
3. **Add Features**: Extend the API for additional functionality
4. **Mobile Optimization**: Enhance responsive design for mobile devices

---

**Ready to test?** Run `python start_web.py` and open http://localhost:5000!
