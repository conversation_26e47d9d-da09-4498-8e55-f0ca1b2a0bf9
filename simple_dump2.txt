

This organ thoughts on what needs to be done and needs to be considered so we will have to clone the controller board so that is a task on its own. The question is when you are cloning, it should be consider integrating the thermal cycle controller, and the motor magnet controllers Into the same design so should be one board should be two boards. Should be two boards with several controller boards that are all set so one option is just on the outside of the DMF controller. We have the PCR controller the MCU on the PCR controller is controlling the motors so we can just draw some pins out and three pins for each motor and the same pins can be repurposed to Dr. electromagnet so three pins for one motor for sure that’s gonna be the moving axis and then there are three other pins coming out for the second water if you are mounting another permanent magnet, we can instead of using a second water replaced that bouncing of the permanent magnet onto the second motor with an electromagnet, which only requires two pens the same pins could be reprogrammed provided select them carefully to do either electromagnet control or water control for a permanent magnet. The drivers for electromagnet and the linear motor separates are small modular boards that are available off the shelf so we need to ask should be just use these small modular boards that are available off. The shelf are tiny boards or should they also try to integrate them Into the PCR controller it’ll make for a slightly more compact design. I personally think we should wait. just draw the pins out and once we have finalize the electromagnet control versus the permanent magnet control then we integrate the Driver boards onto the PCR controller make one clean implementation.

OK, so once we have the PCR controller, we need to start thinking about the next generation where we will have three PCR modules and we will have a different controller for the bigger ship that is so this is the next generation is the bigger configuration again the motor mount will be the same. It will be a long motor, and the magnet mount will be the same whether it’s electromagnet or power magnet . The question is we have the PCR with the motor control wire pulled out driven by Driver boards two of them one for the main motion and the second one for the magnet control on the main Physio controller. We can have one or three PC zones, depending on whether it’s working in conjunction with small shape or hardship.

So the large chip controller which we have to develop will work with three PCR zone plus 2 controller is one for the motor and one for the magnet

The small chip controller which we are going to clone will work with one TCR zone +2 controllers, one for the motor and one for the magnet 

For demo reasons and prototyping, we can hack one of the PCR boards and draw out six wires hook them up to the motor controls. We will also need to pull out two more wires one for ground and one for five boards the power supply 9 V for the drivers will be separate 


Update to the team guys I’m down to the very last things from a controller perspective . I’ve got one more working for the placement of the magnet the second water which could be used for a permanent magnet raising and lowering that is still to be done. I’m having some challenges there. Alternatively, we can have the electromagnet mountain on this moving motor for that the code is complete.. I have ordered a smaller driver module for the electromagnet. That should be here in a couple of days that combine with a very small driver motor controller will make for very compact ELECTRONICS. I will also try to hack one of the PCR keyboards and pull out six wires and drive the motor control directly from the PCR board that will give us