# Constitution Update Checklist

When amending the constitution (`/memory/constitution.md`), ensure all dependent documents are updated to maintain consistency.

## Templates to Update

### When adding/modifying ANY principle:
- [ ] `/templates/plan-template.md` - Update Constitution Check section
- [ ] `/templates/spec-template.md` - Update if requirements/scope affected
- [ ] `/templates/tasks-template.md` - Update if new task types needed
- [ ] `/specs/*/plan.md` - Update existing plans if affected
- [ ] Update any Cursor/Claude context files as needed

### Principle-specific updates:

#### I. Human-in-the-Loop (NON-NEGOTIABLE):
- [ ] Ensure all templates require user approval steps
- [ ] Update review and confirmation processes
- [ ] Add explicit consent requirements

#### II. Signal to Noise Philosophy:
- [ ] Update prioritization requirements in templates
- [ ] Ensure 80/20 signal-to-noise ratio maintained
- [ ] Add focus and distraction elimination checks

#### III. Conversation-First Architecture:
- [ ] Update templates to emphasize conversation primacy
- [ ] Ensure task-conversation synchronization requirements
- [ ] Add audit trail and traceability checks

#### IV. Client-Side State Ownership:
- [ ] Update data architecture requirements
- [ ] Ensure offline-first operation support
- [ ] Add state persistence and sync requirements

#### V. Simplicity and Clarity:
- [ ] Update complexity avoidance requirements
- [ ] Ensure YAML-based persistence maintained
- [ ] Add natural language processing requirements

## Validation Steps

1. **Before committing constitution changes:**
   - [ ] All templates reference new requirements
   - [ ] Examples updated to match new rules
   - [ ] No contradictions between documents

2. **After updating templates:**
   - [ ] Run through a sample implementation plan
   - [ ] Verify all constitution requirements addressed
   - [ ] Check that templates are self-contained (readable without constitution)

3. **Version tracking:**
   - [ ] Update constitution version number
   - [ ] Note version in template footers
   - [ ] Add amendment to constitution history

## Common Misses

Watch for these often-forgotten updates:
- Spec-kit template updates in `/templates/`
- Existing specification files in `/specs/`
- CLI command help text in `next18hrs_poc.py`
- Web interface user interactions in `index.html`
- Cross-references between documents

## Template Sync Status

Last sync check: 2025-01-09
- Constitution version: 1.0.0
- Templates aligned: ✅ (newly created with spec-kit)
- POC integration: 🚧 (in progress)

---

*This checklist ensures the constitution's principles are consistently applied across all project documentation.*