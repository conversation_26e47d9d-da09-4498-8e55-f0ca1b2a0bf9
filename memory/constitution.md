# Next 18hrs Constitution

## Core Principles

### I. Human-in-the-Loop (NON-NEGOTIABLE)
All AI-generated suggestions must be reviewed and approved by the user. No automation without explicit user consent. The system serves as a clarity tool, not an automation engine. Every task action requires user decision and approval.

### II. Signal to Noise Philosophy
Following <PERSON>' approach: Achieve 80% signal, 20% noise ratio in daily focus. Show only what matters NOW (next 18 hours) while safely storing everything else. Eliminate distractions and maintain laser focus on critical priorities.

### III. Conversation-First Architecture
Conversations are the primary unit of thought and task generation. Tasks are never isolated; they derive from conversation entries. The task list is just a view; the real system is built around contextual threads with full audit trail.

### IV. Client-Side State Ownership
Client owns complete lifecycle, state, and synchronization logic. Backend serves as stateless processing layer for LLM interactions. Single persistent state object maintains all system state with full audit trail and version control.

### V. Simplicity and Clarity
Start simple, avoid premature complexity. Use flat schema for MVP (single conversation). Banking/summarization preserves context while controlling size. Natural language inputs transformed to structured actions only when user is ready.

## Technical Constraints

### Data Architecture
- YAML-based state persistence for simplicity and debuggability
- Single source of truth: persistent state object with full audit trail
- Conversation banking with LLM summarization to control file size
- Archive system preserves original entries for recovery

### LLM Integration
- OpenAI GPT-4o-mini for processing and suggestions
- Token-efficient context strategy: task summaries + recent entries
- External system prompt file with Signal to Noise philosophy
- Debug logging for development and troubleshooting

### Performance Standards
- File size management: automatic banking at 10KB YAML size
- Context efficiency: 90%+ reduction through intelligent banking
- Response time: prioritize clarity over speed
- Memory usage: client-side state management for offline-first operation

## Development Workflow

### Feature Development Process
1. **Specification First**: Use spec-kit templates for all new features
2. **Review Gate**: Constitutional compliance check before implementation
3. **Human Approval**: All AI suggestions require explicit user consent
4. **Iterative Refinement**: Conversation-driven clarification and adjustment

### Quality Gates
- Task mutation confirmation for batch changes
- Banking validation preserves essential context
- State versioning for rollback capability
- Full audit trail for all actions and decisions

### Testing Philosophy
- Focus on user scenario validation
- Integration testing for LLM interactions
- State consistency verification
- Conversation banking accuracy

## Governance

This constitution supersedes all other practices and serves as the foundation for all development decisions. Any feature or implementation that conflicts with these principles must be rejected or redesigned.

**Amendments require**:
- Documentation of the change rationale
- Impact assessment on existing architecture
- Migration plan for affected components
- User communication for behavioral changes

**Version**: 1.0.0 | **Ratified**: 2025-01-09 | **Last Amended**: 2025-01-09