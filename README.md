# Next 18hrs – Task Organizer App

## 🧠 Concept & Philosophy

**Next 18hrs** is a human-first, LLM-assisted clarity tool inspired by <PERSON>' Signal to Noise philosophy. It helps users transform mental clutter into clear, prioritized action—without automation. It is designed to reduce decision fatigue, clarify next steps, and separate the act of planning from execution.

The name "Next 18hrs" reflects the core idea: show the user only what matters *now*, while safely storing everything else.

### 🎯 **<PERSON>s Signal to Noise Philosophy**
Based on <PERSON>' approach from the early '90s:
- **SIGNAL:** The top 3-5 critical tasks that MUST be accomplished in the next 18 hours
- **NOISE:** Everything else that distracts from these critical priorities  
- **GOAL:** Achieve 80% signal, 20% noise ratio in daily focus

This philosophy ensures users maintain laser focus on what truly matters, eliminating distractions and maintaining high productivity levels.

### 🔑 **Core Workflow**
> Dump → Digest → Decide → Do

- **Dump**: Users unload unstructured thoughts, intentions, and goals.
- **Digest**: The app (via LLM) clarifies input, asks smart follow-ups, and suggests actionable tasks.
- **Decide**: Users accept, refine, or reject suggestions. Nothing happens without user approval.
- **Do**: Accepted tasks are surfaced as the day's "Signal"—a focused list of what to act on.

---

## 🏗️ Architecture

### 🧠 **Core Design Principles**

- **Conversations are the primary unit** of thought and task generation.
- **Tasks are never isolated**; they are derived from conversation entries.
- **The task list is just a view**; the real system is built around contextual threads.
- **Client-Side as Brain**: The client owns the complete lifecycle, state, and synchronization logic.
- **Backend as Stateless Digestor**: The backend serves as a stateless processing layer for LLM interactions.
- **Persistent Single-Source-of-Truth**: A single state object maintains all system state with full audit trail.

### 🧩 **Data Architecture**

#### **Client-Side State Management**
The client maintains a persistent single-source-of-truth state object that contains:
- **Complete conversation history** with all entries and metadata
- **Task registry** with full lifecycle tracking
- **System audit trail** of all actions and state changes
- **Version control** for data migration and rollback capabilities

**Key Benefits:**
- **Offline-first operation** - All data persists locally
- **Full audit trail** - Every action is timestamped and logged
- **State consistency** - Single object prevents sync conflicts
- **Data ownership** - User controls their complete data

#### **Backend as Stateless Digestor**
The backend serves purely as a processing layer:
- **LLM integration** - Handles API calls and response parsing
- **No persistent state** - Each request is independent
- **Stateless processing** - Scales horizontally without state management
- **Data transformation** - Converts user input to structured tasks

### 🧠 **Persistent State Object Architecture**

For the MVP, the app uses a **single running conversation** and a flat state schema. This keeps things simple and avoids premature complexity. Conversation entries and tasks are stored as parallel arrays, with lightweight metadata for versioning and sync. When the conversation gets too long, older entries are periodically banked and summarized to control LLM context size.

#### **MVP Schema (Now)**

```javascript
{
  entries: [ /* all conversation entries, in order */ ],
  tasks: [ /* all tasks, each linked to an entry */ ],
  meta: {
    version: string,
    last_sync: ISO8601,
    // ...other metadata
  }
}
```

- **entries[]**: Each entry is a user, AI, or system message. All UI or natural language actions are appended here.
- **tasks[]**: Each task is linked to its originating entry. All task actions (add, complete, delete, edit) are also logged as system entries.
- **meta**: Version, sync, and other metadata.

**For now, the app maintains a single running conversation.** Users can dump thoughts, clarify, and accept/modify tasks in a unified thread. When the thread grows too long for LLM context, older entries are banked and summarized, preserving active tasks and key decisions.

This flat schema is easy to persist, migrate, and debug. It supports the core workflow: dump → clarify → decide → do, with full audit trail and traceability.

#### **Extended Schema (Later)**

Once users need to manage multiple concurrent threads (e.g., topic bleed, cross-threading, or explicit project separation), the schema can be extended to support multi-conversation structures:

```javascript
{
  conversations: {
    [conv_id]: {
      id: string,
      title: string,
      entries: Entry[],
      status: 'active' | 'paused' | 'completed' | 'archived',
      // ...other per-conversation metadata
    }
  },
  tasks: {
    [task_id]: {
      id: string,
      description: string,
      is_completed: boolean,
      conversation_id: string,
      entry_id: string,
      created_at: ISO8601,
      updated_at: ISO8601,
      priority: number,
      metadata: object
    }
  },
  active_conversation_id: string,
  version: string,
  last_sync: ISO8601
}
```

**When to extend:**  
- If users want to split work/life, or manage truly separate projects  
- If topic bleed or cross-thread task referencing becomes common  
- If LLM context limits are regularly hit even after summarization  

This multi-conversation structure allows for linked threads, topic isolation, and advanced features like cross-thread task linking. For now, it's a roadmap item.

### 🔄 **Task-Conversation Synchronization**

In **Next 18hrs**, all tasks are deeply tied to the conversation threads that generate them. This ensures full traceability, reflection, and clarity in execution. Every interaction — whether from natural language or UI — is logged as part of the user's thought process.

#### **Unified Task Flow**

- **Tasks originate from conversations.**  
  Each task is linked to both the conversation and the specific message (entry) that produced it.

- **Every task action becomes a conversation entry.**  
  Whether a user marks a task done, deletes it, modifies it, or adds a new one via UI — it is always reflected in the conversation thread as a system-generated entry.

- **Natural language actions are inferred.**  
  The app detects when a user says things like "I finished that" or "Let's drop the hotel idea" and translates these into `done` or `delete` actions on the relevant task.

- **Banking and summarization of long conversations.**  
  When a thread grows too long, the system "banks" earlier entries into a summarized context, preserving active tasks and decisions while reducing clutter.

- **Linked conversations — Planned (Later).**  Users will be able to intentionally link related threads (e.g., "Work" + "Travel") and LLMs may draw from summaries of linked contexts to avoid duplication.

This approach ensures that the task system never drifts out of sync with user intent — maintaining clarity without requiring excessive structure.

---

## ✨ Features

**Note:** MVP uses a single running conversation with periodic banking/summarization. The banking system intelligently captures multi-topic context in summaries, making separate conversations unnecessary.

### 🧩 **Core Features (Now)**

#### ✅ **Human-in-the-Loop**
- No automation. All AI-generated suggestions are reviewed and approved by the user.

#### ✅ **Daily Signal**
- A clear, curated list of 1–3 high-impact tasks for the next 18 hours.

#### ✅ **Thought Unclutter**
- Users can brain-dump freely. The app helps organize and clarify.

#### ✅ **Clarifying Dialog**
- LLM asks follow-up questions to transform vague inputs into clear steps.

#### ✅ **Dual Views**
- **Signal View**: Just today's tasks.
- **Overview View**: All long-term items, naturally organized by context and relationships.

### 🧱 **Design Challenges & Resolutions**

#### 1. **Task List vs. Conversation Sync**
**Challenge**: How to ensure that tasks (added via UI or LLM) remain tied to their original context without creating duplication or confusion.

**Resolution**: Every task is created from a specific conversation entry and every task action (add, modify, complete, delete) is logged back into the originating conversation as a system-generated entry. This keeps the task list and the conversation thread fully in sync.

#### 2. **Invisible System Logging for Task Edits**
**Challenge**: When tasks are added, completed, deleted, or otherwise edited directly via the UI (not through conversation), how do we maintain a full audit trail and keep the conversation and task list in sync—without cluttering the user-facing conversation thread?

**Resolution**:  
- Every direct task action (add, complete, delete, edit) is recorded as a *system entry* in the conversation, including a timestamp, the action, and the task details.
- These system entries are **invisible in the main conversation view**—they are hidden from the user's standard thread, but always included in the data sent to the LLM for context and synchronization.
- This ensures that the LLM always receives the true, full history of both user and system actions, allowing it to generate an accurate, up-to-date task list from the conversation context.
- The user-facing conversation remains uncluttered, showing only meaningful dialog and visible actions, while the system maintains a complete, auditable log for traceability and recovery.

#### 3. **Task Rewind ("Time Machine")**
**Challenge**: If a user feels their task set has diverged from intent (due to misunderstood inputs, incorrect deletions, etc.), how can they recover without losing work?

**Resolution**: 
- **Version Control**: The persistent state object maintains version history for all changes
- **Rollback Capability**: Users can roll back to any previous state, restoring both tasks and conversation context
- **Selective Recovery**: Option to restore specific tasks or entire conversation states
- **Audit Trail**: Full history of what changed, when, and why for informed rollback decisions

#### 4. **Conversation Banking & Summarization** ✅ **RESOLVED**
**Challenge**: Allowing users to add unlimited entries to a conversation risks degrading performance and losing focus.

**Resolution**: ✅ **IMPLEMENTED** - Intelligent banking system with LLM summaries
- **Entry Threshold**: Conversations are automatically banked after thresholds (10KB file size, 10,000 chars, 50 entries)
- **Intelligent Summarization**: Older entries are "banked" and summarized using LLM processing
- **Task Preservation**: All tasks remain active and accessible despite summarization
- **Context Maintenance**: Summary preserves key decisions and context while reducing cognitive load
- **Archive System**: Original entries are preserved in archives for potential recovery
- **Multi-Topic Capture**: Banking summaries preserve context across different topics and projects

#### 5. **Data Migration & Versioning**
**Challenge**: How to handle schema changes, data format updates, and backward compatibility as the system evolves?

**Resolution**:
- **Schema Versioning**: State object includes version field for migration detection
- **Migration Pipelines**: Automatic data transformation between versions
- **Backward Compatibility**: Support for reading older state formats
- **Data Validation**: Schema validation ensures data integrity after migration
- **Rollback Safety**: Failed migrations can be rolled back without data loss

#### 6. **Endlessly Long Conversations** ✅ **RESOLVED**
**Challenge**: Allowing users to add unlimited entries to a conversation risks degrading performance and losing focus.

**Resolution**: ✅ **IMPLEMENTED** - Same as Conversation Banking & Summarization above
- **Automatic Banking**: System automatically banks conversations when thresholds are exceeded
- **Performance Optimization**: File size reduced by 90%+ through intelligent banking
- **Context Preservation**: LLM summaries maintain conversation continuity
- **Focus Maintenance**: Current conversation stays relevant and focused
- **Archive Recovery**: Original entries preserved for potential restoration

#### 7. **Cross-Conversation Awareness** ✅ **RESOLVED**
**Challenge**: Some tasks naturally span multiple contexts (e.g., Travel + Work), but allowing free cross-referencing could create clutter or cognitive overload.

**Resolution**: ✅ **IMPLEMENTED** - Single conversation model with intelligent banking
- **Single Global Conversation**: Eliminates need for cross-conversation linking
- **Multi-Topic Banking**: LLM summaries capture context across different topics and projects
- **Context Preservation**: Banking summaries maintain relationships between related topics
- **Simplified Architecture**: No complex conversation linking logic required
- **Natural Flow**: Real thinking often jumps between topics, which is now supported naturally

#### 8. **UI vs. Conversational Control** ✅ **RESOLVED**
**Challenge**: Users may act via UI (e.g., marking a task done) or via natural language ("I finished that"), which risks desynchronizing the system's state.

**Resolution**: ✅ **IMPLEMENTED** - All task actions, regardless of how they're triggered, are reflected back into the conversation as structured entries. The system treats UI actions as equivalent to spoken input. Both CLI commands (`complete task_1`) and natural language ("I finished that") are properly synchronized in the conversation thread.

#### 9. **Scalability of Task View Management**
**Challenge**: How to surface the right tasks without overwhelming users with an ever-growing task list.

**Resolution**: Introduced virtual views: 
- "Next 18hrs" for immediate focus, 
- "Overview" for backlog and project grouping, 
- "Conversation View" for context,
- and "Review" for recent completions.

These allow clarity without forcing deletion or premature filtering.

#### 10. **Consistency Without Over-Structuring**
**Challenge**: Many productivity tools over-structure task entry, which deters usage.

**Resolution**: Users can dump thoughts freely. The LLM assists by transforming them into structured actions only when the user is ready, always preserving traceability to the original intent.

### 🧭 **Future Features (Roadmap)**

#### **Task Action Inference from Conversation** ✅ **IMPLEMENTED**
Tasks can be updated through natural language, without UI interaction. For example:
- "I finished that" → `mark as done`
- "Let's drop that one" → `delete`
- "That needs a rewrite" → `modify`

✅ **Current Implementation**: The system uses LLM to infer these actions from natural language input, with all actions properly logged in the conversation thread for full traceability.

#### **Task Breakdown for Procrastination ("Action Sparks Motivation")**
When users feel stuck or overwhelmed by a task, they can click/tap on it to trigger **intelligent task decomposition**. This addresses the common procrastination pattern where tasks feel too large or abstract to start.

**Core Principle**: *Motivation comes from action* — by making the first step smaller and clearer, users can build momentum through quick wins.

**Implementation**:
- **Trigger**: Right-click, long-press, or "Break Down" button on any task
- **LLM Processing**: The AI analyzes the original task and generates 3-5 micro-tasks
- **User Control**: User reviews and accepts/rejects the breakdown before replacement
- **Conversation Sync**: The breakdown request and results are logged in the conversation thread

#### **Workstreams** ✅ **RESOLVED**
Multi-step goals are grouped and tracked naturally through the single global conversation.
- **✅ Implemented**: Single global conversation captures all topics and projects
- **✅ Implemented**: Banking summaries preserve multi-topic context and relationships
- **✅ Implemented**: LLM naturally identifies and maintains task relationships
- **✅ Implemented**: Overview view shows all tasks with natural organization
- **✅ Implemented**: No manual workstream management required
- **✅ Implemented**: Context flows naturally between topics without artificial boundaries

#### **Conversation as a Unified Narrative** ✅ **IMPLEMENTED**
All actions — including UI-based ones — are logged as structured `entry` items within the conversation. This turns the thread into a full reflective history of thought and action.

✅ **Current Implementation**:
- `user`: raw user input (via CLI or file processing)
- `ai`: LLM clarifications or suggestions 
- `system`: task actions or auto-logged updates
- Full audit trail maintained in YAML state with timestamps

#### **Lifecycle & Closure of Conversations**
When is a conversation "done"?

Future plan includes:
- Status: `active`, `paused`, `completed`, `archived`
- Auto-archiving after all tasks complete and no activity for N days
- Periodic LLM-generated summaries for closure or review

#### **LLM Prompting & Context Strategy** ✅ **IMPLEMENTED**
✅ **Current Implementation**:
- External system prompt file (`llm_system_prompt.txt`) with Steve Jobs Signal to Noise philosophy
- Token-efficient strategy: existing tasks as YAML context + user input
- Banking summaries provide efficient context management
- Debug logging (`active_prompt.txt`, `active_response.txt`) for development

#### **Fallback Mode Without LLM**
To support early MVP or offline use:
- Rules-based command detection (e.g., regex for "done", "remove", etc.)
- No AI suggestions, but UI + natural phrasing still allowed

#### **Error Handling and Recovery UX**
Need graceful handling of:
- Mislinked tasks
- Wrong inference
- Accidental edits or deletions

Ideas:
- Undo button or "revert last action"
- Task restore from archive
- Show task history inline in thread

#### **Conversation Templates & Presets**
Future feature:
- Structured starting formats for common patterns:
  - Trip planning
  - Project launch
  - Weekly review
- Allows fast onboarding and consistency

#### **Task Mutation Confirmation**
When LLM suggests changes that modify multiple tasks (e.g., adds 3, removes 2), the user is presented with a `Review` state that includes **Accept** / **Reject** buttons. No changes are committed until explicitly approved by the user. This prevents unintended task mutations and keeps user trust high.

#### **Initial Platform Strategy**
To rapidly validate the core algorithm and task-conversation synchronization, the first version will be built for **desktop** (likely as a web app or Electron app). It will feature a **split view**:

- 🗨️ **Left panel**: Conversation thread  
- ✅ **Right panel**: Task list (Next 18hrs / All / Completed)

This layout allows:
- Clear visibility of task inference from conversation.
- Easy debugging of task sync logic.
- Efficient user flow testing before mobile UI design begins.

---

## 🚀 Progress & Implementation Status (Minimal)

> 📋 **For current issues, bugs, and development tasks, see [issues_tracker.md](issues_tracker.md)**

### ✅ Done
- Conversation-task synchronization
- Tasks linked to conversation entries
- System logging of task actions
- Signal view (top 3 tasks)
- Overview view (all tasks)
- LLM integration (OpenAI GPT-4o-mini)
- CLI interface
- State persistence (YAML, audit trail)
- Active deduplication/consolidation
- Debug logging
- Env-based API keys
- Conversation banking & summarization
- Archive system for entries
- Automatic/manual banking triggers
- Natural language task control

### 🚧 Active
- Task mutation confirmation (review batch changes)
- Time machine (rollback/restore)
- Task breakdown for procrastination
- Split-view web interface (desktop)
- Mobile app
- External integrations (Google Tasks, Calendar)

### 🎯 Next 18hrs (Frontend)
- Task breakdown for procrastination (click to decompose)
- Task mutation confirmation (review batch changes)
- Time machine (rollback/restore)

### 🗺️ Roadmap
- Split-view web interface (desktop)
- Mobile app (Flutter iOS/Android)
- External integrations (Google Tasks, Calendar)
- Enhanced UI/UX features
- Advanced task management features

## 🚀 Quick Start

```bash
# Install dependencies
pip install -r requirements.txt

# Run POC
python next18hrs_poc.py dump_file simple_dump.txt
```

**Key Commands:**
- `dump <text>` - Add thoughts to conversation
- `tasks` - Show top 3 tasks (Signal view)
- `complete <id>` - Mark task done (e.g., `complete 1`)
- `bank` - Manually trigger conversation banking
- `help` - Show all commands

---

## 📚 Appendix 1: Task Mutation Confirmation Strategy

### 🎯 **The Task Mutation Problem**
When the LLM processes user input, it may suggest changes that modify multiple tasks simultaneously:
- **Adding new tasks** based on user input
- **Removing duplicate or obsolete tasks** 
- **Modifying existing tasks** for clarity or consolidation
- **Consolidating similar tasks** into single, high-impact items

Currently, these changes are applied immediately without user review, which could lead to unintended task modifications and reduced user trust.

### 🛡️ **Task Mutation Confirmation Approach**

#### **Core Concept**
Task Mutation Confirmation introduces a **Review state** that presents all proposed task changes to the user before they are applied. This ensures user control and prevents unintended modifications while maintaining the conversational flow.

#### **Review State Workflow**

##### **1. LLM Analysis Phase**
When user input is processed:
- **LLM analyzes** the input against existing tasks
- **Identifies changes** needed (additions, removals, modifications)
- **Generates explanation** for each proposed change
- **Creates preview** of the final task state

##### **2. Review State Presentation**
The system presents a structured review interface:

```
🤖 AI: Based on your input, I suggest these task changes:

📝 ADDITIONS (3 tasks):
• Schedule AC repair appointment - High priority, needs immediate action
• Research visa requirements - Medium priority, research phase
• Call insurance about coverage - Medium priority, administrative

🗑️ REMOVALS (2 tasks):
• "Fix AC" → Removing duplicate of "Schedule AC repair appointment"
• "Travel prep" → Too vague, replacing with specific actionable tasks

✏️ MODIFICATIONS (1 task):
• "Book flight" → "Book flight to Tokyo for March 15-20" (added specific details)

📊 IMPACT SUMMARY:
• Total tasks: 12 → 13 (net +1)
• High priority tasks: 3 → 4
• Duplicates eliminated: 2
• Clarity improved: 1 task

[Accept All] [Reject All] [Preview Final State]
```

##### **3. User Decision Options**

**Accept All**: Apply all suggested changes immediately
- **Use case**: User trusts LLM suggestions and wants quick application
- **Behavior**: All changes applied, conversation continues

**Reject All**: Discard all suggested changes
- **Use case**: User disagrees with suggestions or wants to think more
- **Behavior**: No changes applied, conversation continues with original task state

**Preview Final State**: See exactly how tasks will look after changes
- **Use case**: User wants to verify the outcome before committing
- **Behavior**: Shows complete task list with proposed changes applied

#### **Implementation Strategy**

##### **Phase 1: Basic Review State**
- **Review Interface**: Simple text-based review in CLI
- **Basic Options**: Accept All / Reject All / Preview Final State
- **Change Preview**: Show what will be added/removed/modified
- **Integration**: Integrate with existing `process_with_ai` method

##### **Phase 2: Enhanced Preview**
- **Detailed Preview**: Show complete task list with changes applied
- **Change Explanations**: LLM explains reasoning for each change
- **Impact Analysis**: Show how changes affect task priorities and relationships

##### **Phase 3: Advanced Features**
- **Batch Operations**: Allow selective acceptance of change groups
- **Change History**: Track all mutation decisions for learning
- **Smart Defaults**: Learn user preferences for automatic acceptance

#### **Technical Implementation**

##### **Data Structures**
```python
class TaskMutation:
    def __init__(self):
        self.additions: List[Task] = []
        self.removals: List[Task] = []
        self.modifications: List[TaskModification] = []
        self.explanations: Dict[str, str] = {}
        self.impact_summary: Dict[str, int] = {}

class TaskModification:
    def __init__(self):
        self.original_task: Task
        self.modified_task: Task
        self.change_type: str  # "description", "priority", "consolidation"
        self.reason: str
```

##### **Integration Points**
- **`process_with_ai`**: Generate mutation preview before applying
- **`_process_ai_response`**: Parse LLM response into structured mutations
- **`CLI.show_mutation_review`**: Present review interface to user
- **`CLI.apply_mutations`**: Apply accepted changes to task state
- **`CLI.preview_mutations`**: Show final task state with changes applied

##### **User Experience Flow**
1. **User Input**: User provides input via `dump` or `dump_file`
2. **LLM Processing**: System processes input and identifies needed changes
3. **Mutation Generation**: System creates structured mutation proposal
4. **Review Presentation**: User sees proposed changes with explanations
5. **User Decision**: User accepts, rejects, or previews changes
6. **State Update**: Accepted changes are applied to task state
7. **Conversation Continuity**: Process continues with updated task state

#### **Benefits**

##### **🎯 User Control**
- **Explicit Approval**: No changes without user consent
- **Transparency**: User sees exactly what will change and why
- **Preview Capability**: User can see final state before committing
- **Trust Building**: Clear communication builds user confidence

##### **🛡️ Safety**
- **Prevents Unintended Changes**: No surprises from AI suggestions
- **Batch Protection**: Protects against multiple simultaneous changes
- **Validation**: System validates changes before application
- **Rollback Capability**: Changes can be rejected if needed

##### **🧠 Cognitive Benefits**
- **Reduced Cognitive Load**: User doesn't need to track what changed
- **Clear Decision Making**: Structured presentation aids decision making
- **Learning Opportunity**: User learns from LLM reasoning
- **Confidence Building**: User gains confidence in AI suggestions

#### **Considerations**

##### **🤖 LLM Integration**
- **Structured Output**: LLM must provide structured change proposals
- **Explanation Generation**: LLM must explain reasoning for each change
- **Conflict Resolution**: LLM must handle conflicting suggestions
- **Context Preservation**: Changes must maintain conversation context

##### **⚡ Performance Impact**
- **Additional LLM Call**: Review state may require extra processing
- **User Interaction**: Additional step in user workflow
- **State Management**: More complex state handling during review
- **Error Handling**: Robust error handling for preview mode

##### **🎨 User Experience**
- **Workflow Integration**: Must fit naturally into existing workflow
- **Default Behavior**: Sensible defaults for common scenarios
- **Accessibility**: Review interface must be accessible and clear
- **Mobile Considerations**: Review interface must work on mobile

#### **Future Enhancements**

##### **Advanced Features**
- **Template Responses**: Pre-defined responses for common scenarios
- **Change History**: Track all mutation decisions for learning
- **Smart Defaults**: Learn user preferences for automatic acceptance
- **Batch Templates**: Pre-defined change patterns for common workflows

##### **Integration Opportunities**
- **Natural Language Control**: "Accept all additions but reject removals"
- **Voice Commands**: Voice-based review and modification
- **Gesture Control**: Touch/gesture-based modification on mobile
- **Keyboard Shortcuts**: Quick keyboard shortcuts for common actions

This Task Mutation Confirmation strategy ensures user control and safety while maintaining the conversational, human-in-the-loop approach that defines Next 18hrs.

---

## 📚 Appendix 2: Conversation Banking Strategy
The current system stores **massive amounts of unused data** while only sending a **small task summary** to the LLM:
- **4,304 lines** in `next18hrs_state.yaml`
- **221 conversation entries** (mostly unused)
- **140 tasks** (many duplicates)
- **Only task summaries** sent to LLM (efficient)

### 🏦 **Banking Approach: Intelligent Conversation Management**

#### **Core Concept**
"Banking" is the process of **archiving old conversation entries** while **preserving their essential context** for the LLM. This prevents the conversation from growing indefinitely while maintaining the system's ability to understand context and avoid duplication. The single global conversation approach, combined with intelligent banking summaries, captures multi-topic context without requiring separate conversation threads.

#### **Banking Triggers**
- **Content Length Threshold**: When conversation exceeds 10,000 characters (3,000 words)
- **Entry Threshold**: When conversation exceeds 50 entries OR 5KB of content
- **File Size Threshold**: When YAML file exceeds 10KB
- **Manual Trigger**: User can manually bank conversations

#### **Smart Threshold Calculation**
The banking system uses a three-tier trigger approach:

1. **File Size Priority** (10KB limit): Prevents YAML bloat by aggressively banking when file exceeds 10,240 bytes
2. **Content Length** (10,000 chars = 3,000 words): Banks when conversation content becomes too large
3. **Entry Count** (50 entries OR 5KB): Banks when conversation has too many entries or medium content volume

The system calculates content metrics (characters, words, file size) and applies the most appropriate banking strategy based on which threshold is exceeded first.

#### **Content-Aware Banking**
The banking system adapts its strategy based on the type of threshold exceeded:

- **File Size Exceeded**: Most aggressive banking - keeps only 3,000 characters (~900 words) to get under 10KB limit
- **Content Length Exceeded**: Moderate banking - keeps 5,000 characters (~1,500 words) for optimal context
- **Entry Count Exceeded**: Conservative banking - keeps last 50 entries regardless of content length

This ensures the system maintains the right balance between performance and context preservation based on the specific trigger condition.

#### **Banking Process**

##### **1. Entry Analysis & Categorization**
The system analyzes conversation entries to determine banking eligibility:

- **Content metrics calculation**: Total characters, words, file size, entry count
- **Threshold evaluation**: Checks file size (10KB), content length (10,000 chars), and entry count (50 entries OR 5KB)
- **Priority-based selection**: Applies the most restrictive banking strategy based on which threshold is exceeded first

##### **2. LLM-Generated Summary**
The system uses LLM processing to create intelligent summaries of banked entries:

- **Summary prompt**: Focuses on key decisions, important context, patterns, and unresolved questions
- **Content preservation**: Maintains essential context while reducing volume by 60-90%
- **Context continuity**: Ensures LLM can understand conversation flow despite banking

##### **3. State Transformation**
The system transforms the conversation state by replacing old entries with a summary:

- **Summary entry creation**: Creates a system entry containing the LLM-generated summary
- **Metadata preservation**: Stores banking information (trigger type, original counts, date ranges)
- **Archive creation**: Preserves original entries in a separate archive for potential recovery

#### **Banking Benefits**

##### **🎯 Performance Improvements**
- **Reduced file size**: From 4,304 lines to ~500 lines
- **Faster loading**: Smaller YAML files load quickly
- **Efficient LLM context**: Only relevant context sent to AI
- **Memory optimization**: Lower RAM usage for large conversations

##### **🧠 Cognitive Benefits**
- **Reduced noise**: Old, irrelevant details removed from context
- **Preserved wisdom**: Key insights and decisions maintained
- **Focus maintenance**: Current conversation stays relevant
- **Pattern recognition**: LLM can identify recurring themes

##### **📊 Data Management**
- **Audit trail preservation**: All original entries archived separately
- **Version control**: Banking creates new conversation version
- **Recovery capability**: Original entries can be restored if needed
- **Storage efficiency**: 90%+ reduction in active file size

#### **Implementation Strategy**

##### **Phase 1: Content-Aware Banking (Immediate)**
The first implementation phase focuses on content-aware banking:

- **Dynamic threshold calculation**: Determines banking need based on file size, content length, and entry count
- **Priority-based banking**: Applies the most appropriate banking strategy based on which threshold is exceeded
- **Archive system**: Preserves original entries for potential recovery and audit purposes

##### **Phase 2: Intelligent Banking (Later)**
Future enhancements will include intelligent banking features:

- **Content importance scoring**: Analyze entry importance based on content, task creation, and user interaction
- **Selective banking**: Keep important entries regardless of age while banking less critical content
- **Adaptive thresholds**: Adjust banking frequency based on usage patterns and user behavior

#### **Banking Metadata**

##### **Summary Entry Structure**
```yaml
banked_summary_1:
  id: "banked_summary_1"
  sender: "system"
  content: "LLM-generated summary of key insights..."
  timestamp: "2025-09-01T19:05:08.513401"
  type: "banked_summary"
      metadata:
      banking_trigger: "file_size"  # "file_size", "content_length", or "entry_count"
      trigger_value: 15360  # bytes, characters, or entry count
      trigger_secondary: 12500  # secondary metric (chars, words, etc.)
      original_entries_count: 45
      original_chars_count: 12500
      original_words_count: 3750  # ~3,750 words
      original_file_size: 15360  # bytes
      original_date_range: "2025-08-25 to 2025-08-30"
      preserved_chars: 3000  # or 5000 for content_length trigger
      preserved_words: 900  # or 1500 for content_length trigger
      preserved_entries: 8  # or 12 for content_length trigger
      preserved_tasks: 12
      key_decisions: ["AC repair scheduled", "Visa process initiated"]
      recurring_themes: ["Home maintenance", "Travel planning"]
      content_efficiency: 0.24  # chars preserved / total chars (0.24 for file_size, 0.6 for content_length)
```

##### **Archive Structure**
```yaml
archives:
  conv_1_banking_20250901:
    conversation_id: "conv_1"
    banking_date: "2025-09-01T19:05:08.513401"
    original_entries: [/* full original entries */]
    summary_entry: "banked_summary_1"
    metadata: {/* banking metadata */}
```

#### **Banking Considerations**

##### **🤖 LLM Context Strategy**
- **Summary + Recent**: Send banking summary + last 20 entries to LLM
- **Task Preservation**: Ensure all active tasks remain in context
- **Context Continuity**: Maintain conversation flow despite banking
- **Duplicate Prevention**: Use banking summary to prevent task duplication
- **Multi-Topic Capture**: Banking summaries preserve context across different topics and projects

##### **🔄 Recovery & Rollback**
- **Archive Access**: Users can view original entries if needed
- **Selective Restore**: Restore specific entries or entire banking
- **Version History**: Track all banking operations for audit
- **Emergency Rollback**: Revert banking if summary loses important context

##### **📈 Scaling Strategy**
- **File Size Priority**: Primary trigger at 10KB to prevent YAML bloat
- **Content-Based Banking**: Secondary trigger at 10,000 chars (3,000 words)
- **Dual Threshold**: Tertiary trigger on 50 entries OR 5KB of content (whichever comes first)
- **Adaptive Thresholds**: Adjust limits based on usage patterns and LLM context limits
- **Progressive Banking**: Bank in stages (25%, 50%, 75% of content) based on content volume
- **User Control**: Allow users to set content thresholds and banking preferences
- **Single Conversation Model**: Eliminates complexity of cross-conversation management

#### **Banking Impact on Current System**

##### **Immediate Benefits**
- **File size reduction**: From 4,304 lines to ~500 lines
- **Performance improvement**: Faster loading and processing
- **Context clarity**: LLM receives focused, relevant information
- **Storage efficiency**: 90%+ reduction in active storage

##### **Long-term Benefits**
- **Scalability**: System can handle years of conversations
- **Maintainability**: Easier to debug and maintain
- **User experience**: Faster response times and clearer context
- **Data integrity**: Preserved audit trail with efficient storage
- **Simplicity**: Single conversation model reduces complexity and cognitive load

### 🎯 **Next Steps**
1. ✅ **Basic banking implemented** with 50-entry threshold
2. ✅ **LLM summary generation** for banked entries
3. ✅ **Archive system** for original entries
4. ✅ **Banking metadata** for tracking and recovery
5. ✅ **Tested with current conversation** - validated approach

**✅ Banking Implementation Complete!** The system now automatically manages conversation size, reducing file bloat by 90%+ while preserving essential context and maintaining full audit capabilities.

### 🔧 **How Banking Works in the App**

#### **Automatic Banking Triggers**
The app automatically checks for banking conditions on every conversation update:

1. **File Size Check**: Before saving state, the app estimates YAML file size
2. **Content Analysis**: Calculates total characters, words, and entry count
3. **Threshold Evaluation**: Compares against 10KB file limit, 10,000 char content limit, or 50 entry limit
4. **Banking Decision**: Triggers banking if any threshold is exceeded

#### **Banking Process Flow**
When banking is triggered, the app follows this sequence:

1. **Pause Conversation**: Temporarily stops accepting new input
2. **Analyze Content**: Identifies which entries to bank vs. keep
3. **Generate Summary**: Calls LLM to create intelligent summary of banked entries
4. **Update State**: Replaces old entries with summary entry
5. **Archive Original**: Stores original entries in separate archive file
6. **Resume Conversation**: Continues normal operation with reduced file size

#### **User Experience During Banking**
- **Transparent Process**: Banking happens automatically in the background
- **No Data Loss**: All original entries preserved in archive
- **Context Preservation**: LLM summary maintains conversation continuity
- **Performance Boost**: Faster loading and processing after banking

#### **Banking Metadata Tracking**
The app tracks detailed banking information:

- **Trigger Type**: Which threshold caused banking (file_size, content_length, entry_count)
- **Original Metrics**: Character count, word count, entry count before banking
- **Preserved Content**: What was kept vs. what was banked
- **Archive Reference**: Link to archived original entries
- **Content Efficiency**: Ratio of preserved vs. total content

#### **Recovery and Rollback**
Users can access banking history and recover content:

- **Archive Browser**: View all banked conversations and their summaries
- **Selective Restore**: Restore specific entries or entire banking operations
- **Version History**: Track all banking operations with timestamps
- **Emergency Rollback**: Revert banking if summary loses important context

#### **Integration with Existing Features**
Banking integrates seamlessly with current app features:

- **Task Preservation**: All active tasks remain accessible despite banking
- **Conversation Continuity**: LLM context includes banking summary + recent entries
- **CLI Commands**: Banking commands available (`bank`, `bank --force`)
- **Debug Logging**: Banking operations logged to `active_prompt.txt` and `active_response.txt`
- **Archive Files**: Original entries preserved in `archives/` directory

#### **Performance Impact**
Banking provides immediate performance benefits:

- **File Size Reduction**: 90%+ reduction in YAML file size (from 4,304 lines to ~500 lines)
- **Loading Speed**: Faster app startup and state loading
- **Memory Usage**: Lower RAM consumption for large conversations
- **LLM Efficiency**: Reduced context size for better AI performance
- **Automatic Management**: No manual intervention required

---

## 🧱 Tech Stack

| Layer | Technology |
|-------|------------|
| LLM | OpenAI GPT-4o-mini |
| State | YAML persistence |
| CLI | Python 3.8+ |
