

# Issues & Progress Tracker (Minimal)

> 📖 **For project documentation and architecture, see [README.md](README.md)**

## ✅ Done
- Task completion confirmation in conversation thread
- System prompt externalized to file
- Multiple task views (Signal/Overview)
- Basic undo via conversation history
- Natural language task control ("I finished that", "drop this task")

## 🚧 Active
- CLI response delays (OpenAI lag, no progress indicators)
- No streaming/real-time feedback
- No visual highlighting for task changes in CLI
- Large file processing lacks progress indication
- Task deduplication not robust
- All tasks default to P1; no smart prioritization
- Context window limits on large inputs
- API key uses .env (needs improvement)
- Limited input validation and error recovery

## 🎯 Next 18hrs (Frontend)
- Task breakdown for procrastination (click to decompose)
- Task mutation confirmation (review state for batch changes)
- Smart priority assignment (LLM-based 1-3 scale)
- Better deduplication across conversations
- Web interface development (split view)

## 🅿️ Parking lot
- Enhanced button styling (Web)
- Real-time task list updates (Web)
- Progress indicators for AI requests (Web)
- Drag-and-drop prioritization
- Keyboard shortcuts
- Mobile app
- External integrations (Google Tasks, Calendar)