# Implementation Plan: Next 18hrs POC Enhancement

**Branch**: `001-next-18hrs-poc-enhancement` | **Date**: 2025-01-09 | **Spec**: [link](./spec.md)
**Input**: POC enhancement specification from `/specs/001-next-18hrs-app/spec.md`

## Summary
Enhance the existing working POC by adding missing features: task mutation confirmation, time machine rollback, and task breakdown for procrastination. The POC already has a solid foundation with CLI (`next18hrs_poc.py`), web API (`web_server.py`), and split-view interface (`index.html`). Focus on integrating new features seamlessly with existing architecture.

## Technical Context
**Language/Version**: Python 3.8+ (existing POC base)  
**Primary Dependencies**: OpenAI GPT-4o-mini (existing), PyYAML (existing), Flask (existing), Rich (for CLI enhancements)  
**Storage**: YAML state persistence (existing) + version control for rollback  
**Testing**: pytest for new features, integration tests for POC enhancement  
**Target Platform**: Enhance existing CLI + web interface (desktop focus)  
**Project Type**: single - enhance existing POC architecture  
**Performance Goals**: <2s for new feature responses, maintain existing performance  
**Constraints**: Preserve existing functionality, backward compatibility required  
**Scale/Scope**: Enhance existing single-user POC, maintain current data structures

## Constitution Check
*GATE: Must pass before Phase 0 research. Re-check after Phase 1 design.*

**Simplicity**:
- Projects: 1 (enhance existing POC) ✓
- Using framework directly? (enhance existing Flask API, extend existing vanilla JS) ✓
- Single data model? (preserve existing unified state object) ✓
- Avoiding patterns? (extend existing direct approach, no new abstractions) ✓

**Architecture**:
- EVERY feature as library? (enhance existing next18hrs_poc.py as main library) ✓
- Libraries listed: 
  - next18hrs_poc.py (existing + new mutation/rollback/breakdown features)
  - web_server.py (existing + new API endpoints)
  - index.html (existing + new UI controls)
- CLI per library: extend existing CLI commands in `next18hrs_poc.py`
- Library docs: maintain existing documentation approach

**Testing (NON-NEGOTIABLE)**:
- RED-GREEN-Refactor cycle enforced? (tests before implementation) ✓
- Git commits show tests before implementation? (required)
- Order: Contract→Integration→E2E→Unit strictly followed? ✓
- Real dependencies used? (actual YAML files, real LLM calls, real web requests)
- Integration tests for: conversation-task sync, web API, state persistence
- FORBIDDEN: Implementation before test, skipping RED phase

**Observability**:
- Structured logging included? (debug logs for all operations)
- Frontend logs → backend? (unified logging stream from web to API)
- Error context sufficient? (full conversation context on errors)

**Versioning**:
- Version number assigned? (v1.0.0 for complete app)
- BUILD increments on every change? (semantic versioning)
- Breaking changes handled? (migration scripts, backward compatibility)

## Project Structure

### Documentation (this feature)
```
specs/001-next-18hrs-app/
├── plan.md              # This file (/plan command output)
├── research.md          # Phase 0 output (/plan command)
├── data-model.md        # Phase 1 output (/plan command)
├── quickstart.md        # Phase 1 output (/plan command)
├── contracts/           # Phase 1 output (/plan command)
│   ├── api-spec.json    # Backend API specification
│   └── frontend-spec.md # Frontend interface specification
└── tasks.md             # Phase 2 output (/tasks command - NOT created by /plan)
```

### Source Code (repository root)
```
# Option 2: Web application (backend + frontend)
backend/
├── src/
│   ├── models/
│   │   ├── conversation.py     # Conversation entity
│   │   ├── task.py            # Task entity  
│   │   └── state.py           # Application state
│   ├── services/
│   │   ├── conversation_manager.py  # Conversation logic
│   │   ├── task_engine.py          # Task management
│   │   ├── llm_processor.py        # AI integration
│   │   └── state_manager.py        # State persistence
│   ├── api/
│   │   ├── conversation_api.py     # Conversation endpoints
│   │   ├── task_api.py            # Task endpoints
│   │   └── app.py                 # Main API application
│   └── cli/
│       └── next18hrs_cli.py       # Enhanced CLI (extends existing)
└── tests/
    ├── contract/
    │   ├── test_api_contracts.py   # API contract tests
    │   └── test_cli_contracts.py   # CLI contract tests
    ├── integration/
    │   ├── test_conversation_task_sync.py  # Core sync tests
    │   ├── test_web_api_flow.py           # Web interface tests
    │   └── test_state_persistence.py      # State management tests
    └── unit/
        ├── test_conversation_manager.py
        ├── test_task_engine.py
        ├── test_llm_processor.py
        └── test_state_manager.py

frontend/
├── src/
│   ├── components/
│   │   ├── conversation-panel.js   # Left panel conversation
│   │   ├── task-panel.js          # Right panel tasks
│   │   └── app.js                 # Main application
│   ├── pages/
│   │   └── index.html             # Main app page
│   └── services/
│       ├── api-client.js          # Backend API calls
│       └── state-sync.js          # Real-time synchronization
└── tests/
    ├── integration/
    │   └── test_ui_flow.js         # User interface tests
    └── unit/
        ├── test_conversation_panel.js
        └── test_task_panel.js

# Keep existing files
next18hrs_poc.py     # Current CLI (migrate to backend/src/cli/)
next18hrs_state.yaml # Current state (migrate to backend data/)
llm_system_prompt.txt # Keep as shared resource
```

**Structure Decision**: Option 2 (Web application) - frontend + backend with enhanced CLI

## Phase 0: Outline & Research

1. **Extract unknowns from Technical Context** above:
   - Research best practices for Flask/FastAPI backend architecture
   - Research real-time web UI patterns for conversation-task synchronization
   - Research offline-first web application strategies
   - Research migration strategies for existing CLI to web architecture

2. **Generate and dispatch research agents**:
   ```
   Task: "Research Flask vs FastAPI for real-time web APIs with WebSocket support"
   Task: "Find best practices for offline-first web applications with local storage"
   Task: "Research patterns for real-time UI synchronization in vanilla JavaScript"
   Task: "Research migration strategies for CLI applications to web architecture"
   ```

3. **Consolidate findings** in `research.md` using format:
   - Decision: FastAPI for async support, vanilla JS for simplicity
   - Rationale: Real-time requirements, avoiding framework complexity
   - Alternatives considered: Flask + SocketIO, React frontend

**Output**: research.md with web architecture and migration strategy decisions

## Phase 1: Design & Contracts
*Prerequisites: research.md complete*

1. **Extract entities from feature spec** → `data-model.md`:
   - Conversation: entries, metadata, banking status
   - Task: description, status, conversation linkage, priority
   - State: complete application state with version control
   - User interactions and API interfaces

2. **Generate API contracts** from functional requirements:
   - **Conversation API**: GET /conversations, POST /conversations/entries, GET /conversations/{id}
   - **Task API**: GET /tasks, POST /tasks, PUT /tasks/{id}, DELETE /tasks/{id}
   - **LLM API**: POST /llm/process, POST /llm/clarify
   - **State API**: GET /state, POST /state/backup, POST /state/restore
   - **WebSocket**: Real-time updates for conversation and task changes

3. **Generate contract tests** from contracts:
   - Test all API endpoints with expected request/response schemas
   - Test WebSocket real-time update behavior
   - Test CLI command compatibility with new backend
   - Tests must fail (no implementation yet)

4. **Extract test scenarios** from user stories:
   - Brain dump → LLM processing → task generation flow
   - Split-view interface with real-time synchronization
   - Task mutation confirmation and time machine workflows
   - Conversation banking and state persistence

5. **Update agent file incrementally**:
   - Run `/scripts/update-agent-context.sh claude`
   - Add web application patterns and API design context
   - Update with real-time synchronization requirements

**Output**: data-model.md, /contracts/*, failing tests, quickstart.md, updated CLAUDE.md

## Phase 2: Task Planning Approach
*This section describes what the /tasks command will do - DO NOT execute during /plan*

**Task Generation Strategy**:
- Load `/templates/tasks-template.md` as base
- Generate tasks from Phase 1 design docs
- Backend tasks: API endpoints, service libraries, data models
- Frontend tasks: UI components, real-time sync, user interactions
- Migration tasks: CLI integration, state migration, testing
- Each contract → contract test task [P]

**Ordering Strategy**:
- TDD order: Tests before implementation 
- Dependency order: Models → Services → API → Frontend → Integration
- Backend and Frontend development can proceed in parallel [P]
- Migration tasks depend on backend completion

**Estimated Output**: 40-50 numbered, ordered tasks in tasks.md

**IMPORTANT**: This phase is executed by the /tasks command, NOT by /plan

## Phase 3+: Future Implementation
*These phases are beyond the scope of the /plan command*

**Phase 3**: Task execution (/tasks command creates tasks.md)  
**Phase 4**: Implementation (execute tasks.md following constitutional principles)  
**Phase 5**: Validation (run tests, execute quickstart.md, performance validation)

## Complexity Tracking
*Fill ONLY if Constitution Check has violations that must be justified*

| Violation | Why Needed | Simpler Alternative Rejected Because |
|-----------|------------|-------------------------------------|
| None identified | N/A | N/A |

## Progress Tracking
*This checklist is updated during execution flow*

**Phase Status**:
- [ ] Phase 0: Research complete (/plan command)
- [ ] Phase 1: Design complete (/plan command)
- [ ] Phase 2: Task planning complete (/plan command - describe approach only)
- [ ] Phase 3: Tasks generated (/tasks command)
- [ ] Phase 4: Implementation complete
- [ ] Phase 5: Validation passed

**Gate Status**:
- [x] Initial Constitution Check: PASS
- [ ] Post-Design Constitution Check: PASS
- [ ] All NEEDS CLARIFICATION resolved
- [ ] Complexity deviations documented

---
*Based on Constitution v1.0.0 - See `/memory/constitution.md`*
