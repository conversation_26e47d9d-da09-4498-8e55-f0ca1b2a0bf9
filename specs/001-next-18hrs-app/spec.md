# Application Specification: Next 18hrs POC Enhancement

**Feature Branch**: `001-next-18hrs-poc-enhancement`  
**Created**: 2025-01-09  
**Status**: Draft  
**Input**: Enhance existing working POC (CLI + Web interface) with missing features: time machine rollback, task breakdown for procrastination, dynamic framework selection (<PERSON>, GT<PERSON>, <PERSON>, etc.), and improved web UI polish.

## Execution Flow (main)
```
1. Parse user description from Input
   → Validated: Complete productivity app specification from README
2. Extract key concepts from description
   → Actors: Users, LLM (GPT-4o-mini), App Interface (CLI/Web/Mobile)
   → Actions: Dump thoughts, digest via LLM, decide on tasks, execute actions
   → Data: Conversations, tasks, state management, audit trail
   → Constraints: Human-in-the-loop, no automation, conversation-first architecture
3. For each unclear aspect:
   → App architecture is well-defined in README
4. Fill User Scenarios & Testing section
   → Clear user flow: Mental dump → LLM clarification → Task generation → Execution
5. Generate Functional Requirements
   → Based on existing CLI + planned web/mobile interfaces
6. Identify Key Entities
   → Conversation, Task, State, User, LLM
7. Run Review Checklist
   → Spec captures complete app vision and architecture
8. Return: SUCCESS (spec ready for planning)
```

---

## ⚡ Quick Guidelines
- ✅ Focus on WHAT users need and WHY
- ❌ Avoid HOW to implement (no tech stack, APIs, code structure)
- 👥 Written for business stakeholders, not developers

---

## User Scenarios & Testing *(mandatory)*

### Primary User Story
As a user of the existing Next 18hrs POC, I want the missing features (rollback capability, task breakdown, framework selection) added to the current CLI and web interface, so that I can organize my thoughts using different productivity approaches (Signal to Noise, GTD, Eisenhower, etc.) while maintaining complete control over my task management workflow.

### Acceptance Scenarios

#### POC Enhancement Scenarios
1. **Given** I realize my task list has diverged from my intent, **When** I use the time machine feature, **Then** I can rollback to any previous state without losing work

2. **Given** I have a task that feels overwhelming, **When** I trigger task breakdown (CLI or web), **Then** the app decomposes it into 3-5 actionable micro-tasks

3. **Given** I want to organize my tasks differently, **When** I select a framework (GTD, Eisenhower, etc.), **Then** the AI processes my thoughts using that organizational approach instead of Signal to Noise

4. **Given** I'm using the web interface, **When** I change frameworks, **Then** I see a dropdown selector with available frameworks (Signal, GTD, Eisenhower, Essentialism, etc.) and the conversation continues with the new organizational lens

5. **Given** I'm using the CLI, **When** I use `dump --framework gtd "my thoughts"`, **Then** the AI processes my input using Getting Things Done principles and suggests tasks organized by contexts and projects

6. **Given** I want to see what frameworks are available, **When** I check the frameworks directory or use help, **Then** I can see all supported productivity methodologies

6. **Given** I'm using any new features, **When** they're triggered via web interface, **Then** the split-view updates show both conversation and task changes in real-time

#### Current POC Validation
7. **Given** I use the existing web interface at localhost:8080, **When** I dump thoughts and interact with tasks, **Then** the conversation-task synchronization works as designed

8. **Given** I use the existing CLI commands, **When** I add the new features, **Then** they integrate seamlessly with the current workflow

9. **Given** I have existing state files, **When** I upgrade to enhanced POC, **Then** all my data is preserved and enhanced features work with existing conversations

### Edge Cases
- What happens when LLM is unavailable but user needs to continue working?
- How does the app handle very large conversation histories spanning months?
- What happens when users want to separate work and personal task management?
- How does the app behave with poor internet connectivity?

## Requirements *(mandatory)*

### Functional Requirements

#### POC Enhancement Requirements (Missing Features)
- **FR-001**: System MUST add time machine (rollback) functionality to existing state management
- **FR-002**: System MUST provide task breakdown capability accessible from CLI and web interface
- **FR-003**: System MUST integrate new features seamlessly with existing conversation-task synchronization

#### Current POC Preservation
- **FR-006**: System MUST preserve all existing CLI functionality in `next18hrs_poc.py`
- **FR-007**: System MUST maintain existing web interface split-view design in `index.html`
- **FR-008**: System MUST keep current YAML state persistence and conversation banking
- **FR-009**: System MUST preserve existing LLM integration and system prompt functionality
- **FR-010**: System MUST maintain backward compatibility with existing state files

#### Web Interface Enhancements
- **FR-011**: Web interface MUST add buttons/controls for task breakdown and time machine features
- **FR-012**: Web interface MUST maintain real-time synchronization for new features
- **FR-013**: Web interface MUST provide visual feedback for rollback operations and task breakdowns

#### CLI Interface Enhancements
- **FR-014**: CLI MUST add time machine commands (`rollback`, `history`, `restore`)
- **FR-015**: CLI MUST add task breakdown commands (`breakdown <task_id>`)
- **FR-016**: CLI MUST integrate new commands with existing help and command structure

#### Framework Selection Requirements
- **FR-017**: Web interface MUST provide framework selector dropdown (Signal, GTD, Eisenhower, etc.)
- **FR-018**: CLI MUST support framework flag (`dump --framework gtd "thoughts"`)
- **FR-019**: System MUST load framework content from existing `/frameworks/` directory
- **FR-020**: System MUST build prompts client-side using selected framework content
- **FR-021**: System MUST remember user's framework preference across sessions
- **FR-022**: Framework switching MUST preserve conversation context and maintain audit trail

### Key Entities *(include if feature involves data)*
- **Conversation**: Primary container for all user thoughts, LLM interactions, and system actions
- **Task**: Actionable items derived from conversation entries with lifecycle tracking
- **State**: Complete application state including conversations, tasks, and metadata
- **User**: Person using the app to manage tasks and maintain focus
- **LLM**: AI assistant that processes inputs and suggests clarifications and tasks
- **Framework**: Productivity methodology container (Signal, GTD, Eisenhower, etc.) that defines task organization approach

---

## Review & Acceptance Checklist
*GATE: Automated checks run during main() execution*

### Content Quality
- [x] No implementation details (languages, frameworks, APIs)
- [x] Focused on user value and business needs
- [x] Written for non-technical stakeholders
- [x] All mandatory sections completed

### Requirement Completeness
- [x] No [NEEDS CLARIFICATION] markers remain
- [x] Requirements are testable and unambiguous  
- [x] Success criteria are measurable
- [x] Scope is clearly bounded (productivity app with specific philosophy)
- [x] Dependencies and assumptions identified

---

## Execution Status
*Updated by main() during processing*

- [x] User description parsed
- [x] Key concepts extracted
- [x] Ambiguities marked (none found)
- [x] User scenarios defined
- [x] Requirements generated
- [x] Entities identified
- [x] Review checklist passed

---
