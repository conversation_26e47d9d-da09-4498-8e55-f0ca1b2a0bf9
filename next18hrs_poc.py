#!/usr/bin/env python3
"""
Next 18hrs POC - Single File Task Organizer
Demonstrates conversation-task synchronization using OpenAI GPT-4o-mini.

Core Concept: Dump → Digest → Decide → Do
- Dump: Type your thoughts freely
- Digest: AI processes and suggests tasks  
- Decide: You see what tasks were created
- Do: Focus on the "Signal" view (top 3 tasks)

Usage:
    python next18hrs_poc.py                    # Interactive mode
    python next18hrs_poc.py dump_file <file>   # Process file directly
    python next18hrs_poc.py help               # Show help
"""

import json
import yaml
import os
import sys
from datetime import datetime
from typing import List, Dict, Optional

try:
    import openai
    from dotenv import load_dotenv
except ImportError as e:
    if "openai" in str(e):
        print("❌ OpenAI not installed. Install with: pip install openai")
    elif "dotenv" in str(e):
        print("❌ python-dotenv not installed. Install with: pip install python-dotenv")
    else:
        print(f"❌ Import error: {e}")
    sys.exit(1)


class Next18HrsPOC:
    """Single-file POC for Next 18hrs task organizer."""
    
    def __init__(self):
        self.conversations: Dict[str, dict] = {}
        self.tasks: Dict[str, dict] = {}
        self.active_conversation_id: Optional[str] = None
        self.model = "gpt-4o-mini"
        self.system_prompt = self._load_system_prompt()
        self._setup_openai()
        self.load_state()
    
    def _load_system_prompt(self) -> str:
        """Load the system prompt from file."""
        prompt_file = "llm_system_prompt.txt"
        if not os.path.exists(prompt_file):
            print(f"❌ Prompt file {prompt_file} not found!")
            print(f"Please create {prompt_file} with your system prompt.")
            sys.exit(1)
        
        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                prompt = f.read().strip()
            print(f"✅ Loaded system prompt from {prompt_file}")
            return prompt
        except Exception as e:
            print(f"❌ Error loading prompt file: {e}")
            sys.exit(1)
    


    def _setup_openai(self):
        """Setup OpenAI connection."""
        # Load environment variables from .env file
        load_dotenv()
        
        # Check for API key
        api_key = os.getenv('OPENAI_API_KEY')
        if not api_key:
            print("❌ OPENAI_API_KEY not found in .env file!")
            print("Please create a .env file with: OPENAI_API_KEY=your-key-here")
            sys.exit(1)
        
        # Always use gpt-4o-mini
        self.model = "gpt-4o-mini"
        print(f"✅ Using {self.model} with OpenAI")
    
    def create_conversation(self, title: str = "New Conversation") -> str:
        """Create a new conversation."""
        conv_id = f"conv_{len(self.conversations) + 1}"
        self.conversations[conv_id] = {
            "id": conv_id,
            "title": title,
            "entries": [],
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat()
        }
        self.active_conversation_id = conv_id
        return conv_id
    
    def add_user_input(self, content: str) -> dict:
        """Add user input to conversation."""
        if not self.active_conversation_id:
            self.create_conversation()
        
        conversation = self.conversations[self.active_conversation_id]
        entry = {
            "id": f"entry_{len(conversation['entries']) + 1}",
            "sender": "user",
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        conversation["entries"].append(entry)
        conversation["updated_at"] = datetime.now().isoformat()
        
        # Check if banking is needed after adding user input
        self.check_and_bank_conversation(self.active_conversation_id)
        
        return entry
    
    def process_with_ai(self, user_input: str) -> dict:
        """Process user input through AI using OpenAI."""
        if not self.model:
            return {"error": "OpenAI model not available. Please ensure OpenAI API key is set in .env file."}
        
        try:
            # Build the prompt for the LLM using loaded system prompt
            # Include existing task context as YAML
            existing_tasks_yaml = self.get_state_as_yaml()
            context_section = f"\n\nEXISTING TASKS CONTEXT:\n```yaml\n{existing_tasks_yaml}```\n\n"
            
            full_prompt = self.system_prompt + context_section + user_input
            
            # Debug: Save the full prompt to file
            try:
                with open("active_prompt.txt", "w", encoding="utf-8") as f:
                    f.write(full_prompt)
                print(f"🔍 Debug: Full prompt saved to active_prompt.txt ({len(full_prompt)} characters)")
                print(f"📊 Included {len(self.tasks)} existing tasks as YAML context")
            except Exception as e:
                print(f"⚠️  Debug: Could not save prompt to file: {e}")
            
            # Call OpenAI
            client = openai.OpenAI()
            response = client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": context_section + user_input}
                ],
                temperature=0.1
            )
            
            ai_response = response.choices[0].message.content.strip()
            
            # Debug: Save the LLM response to file
            try:
                with open("active_response.txt", "w", encoding="utf-8") as f:
                    f.write(ai_response)
                print(f"🔍 Debug: LLM response saved to active_response.txt ({len(ai_response)} characters)")
            except Exception as e:
                print(f"⚠️  Debug: Could not save LLM response to file: {e}")
            
            # Extract tasks and add to conversation
            return self._process_ai_response(ai_response)
            
        except Exception as e:
            return {"error": f"AI processing failed: {str(e)}"}
    

    
    def _process_ai_response(self, ai_response: str) -> dict:
        """Process AI response and extract tasks."""
        # Check if AI provided an existing tasks list
        existing_tasks = []
        new_tasks = []
        
        # Look for existing tasks list (usually marked with "EXISTING TASKS:" or similar)
        if "EXISTING TASKS" in ai_response or "DEDUPLICATED TASK LIST" in ai_response or "CONSOLIDATED TASK LIST" in ai_response or "CONSOLIDATED TASKS" in ai_response:
            # Extract existing tasks
            lines = ai_response.split('\n')
            in_existing_section = False
            
            print(f"🔍 Debug: Found existing tasks section, parsing {len(lines)} lines...")
            
            for i, line in enumerate(lines):
                line = line.strip()
                if "EXISTING TASKS" in line or "DEDUPLICATED TASK LIST" in line or "CONSOLIDATED TASK LIST" in line or "CONSOLIDATED TASKS" in line:
                    in_existing_section = True
                    print(f"🔍 Debug: Entered existing tasks section at line {i}: {line}")
                    continue
                elif "NEW TASKS" in line or "TASK:" in line:
                    in_existing_section = False
                    print(f"🔍 Debug: Exited existing tasks section at line {i}: {line}")
                    break
                elif (line.startswith('-') or line.startswith('1.') or line.startswith('2.') or line.startswith('3.') or line.startswith('4.') or line.startswith('5.') or line.startswith('6.') or line.startswith('7.') or line.startswith('8.') or line.startswith('9.') or line.startswith('10.')) and in_existing_section:
                    # Extract task description from bullet point or numbered list
                    if line.startswith('-'):
                        task_desc = line[1:].strip()
                    else:
                        # Handle numbered lists (1., 2., etc.)
                        task_desc = line.split(':', 1)[1].strip() if ':' in line else line.split('.', 1)[1].strip()
                    
                    if task_desc:
                        # Remove markdown formatting (**) if present
                        task_desc = task_desc.replace('**', '').strip()
                        if task_desc:
                            existing_tasks.append(task_desc)
                            print(f"🔍 Debug: Added existing task: {task_desc}")
            
            print(f"🔍 Debug: Found {len(existing_tasks)} existing tasks")
        
        # Extract new tasks (marked with "TASK:")
        if "TASK:" in ai_response:
            task_parts = ai_response.split("TASK:")
            for task_part in task_parts[1:]:
                task_desc = task_part.split("\n")[0].strip()
                if task_desc:
                    new_tasks.append(task_desc)
        
        # Add AI response to conversation
        conversation = self.conversations[self.active_conversation_id]
        ai_entry = {
            "id": f"entry_{len(conversation['entries']) + 1}",
            "sender": "ai",
            "content": ai_response,
            "timestamp": datetime.now().isoformat()
        }
        conversation["entries"].append(ai_entry)
        conversation["updated_at"] = datetime.now().isoformat()
        
        # If we have existing tasks, replace all existing tasks
        if existing_tasks:
            old_task_count = len(self.tasks)
            self.tasks.clear()  # Remove all existing tasks
            
            # Create new existing tasks
            for i, task_desc in enumerate(existing_tasks):
                task_id = f"task_{i + 1}"
                task = {
                    "id": task_id,
                    "description": task_desc,
                    "is_completed": False,
                    "conversation_id": self.active_conversation_id,
                    "entry_id": ai_entry["id"],
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                    "priority": 1
                }
                self.tasks[task_id] = task
            
            # Log the task replacement
            self.add_system_log(f"Task list updated: {old_task_count} old tasks replaced with {len(existing_tasks)} existing tasks")
            
            # Add any new tasks on top
            for task_desc in new_tasks:
                task_id = f"task_{len(self.tasks) + 1}"
                task = {
                    "id": task_id,
                    "description": task_desc,
                    "is_completed": False,
                    "conversation_id": self.active_conversation_id,
                    "entry_id": ai_entry["id"],
                    "created_at": datetime.now().isoformat(),
                    "updated_at": datetime.now().isoformat(),
                    "priority": 1
                }
                self.tasks[task_id] = task
            
            return {
                "ai_response": ai_response,
                "existing_tasks_updated": len(existing_tasks),
                "new_tasks_added": len(new_tasks),
                "total_tasks": len(self.tasks),
                "message": f"✅ Successfully updated task list: {old_task_count} old tasks replaced with {len(existing_tasks)} existing tasks + {len(new_tasks)} new tasks"
            }
        else:
            # Fallback to old behavior if no consolidation detected
            created_tasks = []
            for task_desc in new_tasks:
                task = self.create_task(task_desc, self.active_conversation_id, ai_entry["id"])
                created_tasks.append(task)
        
        return {
            "ai_response": ai_response,
            "tasks_created": len(created_tasks),
            "tasks": created_tasks
        }
    
    def create_task(self, description: str, conversation_id: str, entry_id: str) -> dict:
        """Create a task from a conversation entry."""
        task_id = f"task_{len(self.tasks) + 1}"
        task = {
            "id": task_id,
            "description": description,
            "is_completed": False,
            "conversation_id": conversation_id,
            "entry_id": entry_id,
            "created_at": datetime.now().isoformat(),
            "updated_at": datetime.now().isoformat(),
            "priority": 1
        }
        self.tasks[task_id] = task
        
        # Log task creation in conversation
        self.add_system_log(f"Task created: '{description}' (ID: {task_id})")
        
        return task
    
    def add_system_log(self, content: str) -> dict:
        """Add system log entry to conversation."""
        if not self.active_conversation_id:
            return {}
        
        conversation = self.conversations[self.active_conversation_id]
        entry = {
            "id": f"entry_{len(conversation['entries']) + 1}",
            "sender": "system",
            "content": content,
            "timestamp": datetime.now().isoformat()
        }
        conversation["entries"].append(entry)
        conversation["updated_at"] = datetime.now().isoformat()
        return entry
    
    def complete_task(self, task_id: str) -> bool:
        """Mark a task as completed."""
        if task_id in self.tasks:
            self.tasks[task_id]["is_completed"] = True
            self.tasks[task_id]["updated_at"] = datetime.now().isoformat()
            
            # Log completion in conversation
            self.add_system_log(f"Task completed: '{self.tasks[task_id]['description']}'")
            return True
        return False
    
    def get_signal_view(self) -> List[dict]:
        """Get the 'Next 18hrs' signal view - top 3 uncompleted tasks."""
        active_tasks = [task for task in self.tasks.values() if not task["is_completed"]]
        sorted_tasks = sorted(active_tasks, key=lambda t: t["priority"], reverse=True)
        return sorted_tasks[:3]
    
    def get_overview_view(self) -> List[dict]:
        """Get all tasks for overview."""
        return list(self.tasks.values())
    
    def get_active_conversation(self) -> Optional[dict]:
        """Get the currently active conversation."""
        if self.active_conversation_id:
            return self.conversations.get(self.active_conversation_id)
        return None
    
    def get_state_as_yaml(self) -> str:
        """Convert current state to YAML format for LLM context."""
        try:
            # Create a simplified state representation
            state = {
                "existing_tasks": [
                    {
                        "id": task["id"],
                        "description": task["description"],
                        "status": "completed" if task["is_completed"] else "active",
                        "priority": task["priority"]
                    }
                    for task in self.tasks.values()
                ],
                "total_tasks": len(self.tasks),
                "active_tasks": len([t for t in self.tasks.values() if not t["is_completed"]]),
                "completed_tasks": len([t for t in self.tasks.values() if t["is_completed"]])
            }
            
            return yaml.dump(state, default_flow_style=False, sort_keys=False)
        except Exception as e:
            return f"Error generating YAML: {str(e)}"
    
    def _estimate_yaml_size(self, conversation: dict) -> int:
        """Estimate YAML file size for banking decisions."""
        try:
            # Create a sample state with this conversation
            sample_state = {
                "conversations": {conversation["id"]: conversation},
                "tasks": self.tasks,
                "active_conversation_id": conversation["id"]
            }
            
            # Convert to YAML and get size
            yaml_content = yaml.dump(sample_state, default_flow_style=False, sort_keys=False)
            return len(yaml_content.encode('utf-8'))
        except Exception as e:
            print(f"⚠️  Error estimating YAML size: {e}")
            return 0
    
    def _calculate_banking_threshold(self, conversation: dict) -> tuple:
        """Calculate if banking is needed based on content metrics."""
        try:
            total_chars = sum(len(entry.get('content', '')) for entry in conversation['entries'])
            total_entries = len(conversation['entries'])
            total_words = total_chars / 3.33  # Approximate words per character
            yaml_file_size = self._estimate_yaml_size(conversation)
            
            # Primary trigger: File size (10KB limit)
            if yaml_file_size > 10240:  # 10KB = 10,240 bytes
                return "file_size", yaml_file_size, total_chars
            
            # Secondary trigger: Content length (10,000 chars = ~3,000 words)
            if total_chars > 10000:
                return "content_length", total_chars, total_words
            
            # Tertiary trigger: Entry count OR content size
            if total_entries > 50 or total_chars > 5000:  # 50 entries OR 5KB
                avg_chars_per_entry = total_chars / total_entries if total_entries > 0 else 0
                if avg_chars_per_entry > 50:  # Only trigger if entries are meaningful
                    return "entry_count", total_entries, total_chars
            
            return None, None, None
        except Exception as e:
            print(f"⚠️  Error calculating banking threshold: {e}")
            return None, None, None
    
    def _generate_banking_summary(self, entries_to_bank: List[dict], trigger_type: str, trigger_value: int) -> str:
        """Generate LLM summary of banked entries."""
        try:
            # Prepare entries for summary
            entries_text = ""
            for entry in entries_to_bank:
                sender = entry.get('sender', 'unknown')
                content = entry.get('content', '')
                timestamp = entry.get('timestamp', '')
                entries_text += f"[{sender}] {timestamp}: {content}\n\n"
            
            # Create summary prompt
            summary_prompt = f"""
Summarize these {len(entries_to_bank)} conversation entries into key insights:

ENTRIES TO SUMMARIZE:
{entries_text}

Focus on:
- Key decisions made
- Important context for current tasks
- Patterns or recurring themes
- Any unresolved questions or concerns

Keep summary under 500 words while preserving essential context.
"""
            
            # Call LLM for summary
            client = openai.OpenAI()
            response = client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that creates concise summaries of conversation entries while preserving essential context and key insights."},
                    {"role": "user", "content": summary_prompt}
                ],
                temperature=0.1
            )
            
            summary = response.choices[0].message.content.strip()
            
            # Add banking metadata
            summary_with_metadata = f"""
BANKED CONVERSATION SUMMARY (Trigger: {trigger_type}, Value: {trigger_value})

{summary}

[Banking Info: {len(entries_to_bank)} entries summarized, {trigger_type} threshold exceeded]
"""
            
            return summary_with_metadata
        except Exception as e:
            print(f"⚠️  Error generating banking summary: {e}")
            return f"Banking summary error: {str(e)}"
    
    def _archive_entries(self, conversation_id: str, entries_to_bank: List[dict], trigger_type: str, trigger_value: int):
        """Archive banked entries for potential recovery."""
        try:
            archive_data = {
                "conversation_id": conversation_id,
                "banking_date": datetime.now().isoformat(),
                "trigger_type": trigger_type,
                "trigger_value": trigger_value,
                "entries_count": len(entries_to_bank),
                "original_entries": entries_to_bank
            }
            
            # Create archives directory if it doesn't exist
            os.makedirs("archives", exist_ok=True)
            
            # Save to archive file
            archive_filename = f"archives/{conversation_id}_banking_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
            with open(archive_filename, 'w') as f:
                yaml.dump(archive_data, f, default_flow_style=False, sort_keys=False)
            
            print(f"📁 Archived {len(entries_to_bank)} entries to {archive_filename}")
        except Exception as e:
            print(f"⚠️  Error archiving entries: {e}")
    
    def bank_conversation(self, conversation_id: str) -> Optional[str]:
        """Bank conversation based on content volume and thresholds."""
        try:
            if conversation_id not in self.conversations:
                print(f"❌ Conversation {conversation_id} not found")
                return None
            
            conversation = self.conversations[conversation_id]
            
            # Calculate content metrics
            total_chars = sum(len(entry.get('content', '')) for entry in conversation['entries'])
            total_entries = len(conversation['entries'])
            
            # Determine if banking is needed
            trigger_type, trigger_value, trigger_secondary = self._calculate_banking_threshold(conversation)
            
            if not trigger_type:
                print(f"ℹ️  No banking needed for conversation {conversation_id}")
                return None
            
            print(f"🏦 Banking conversation {conversation_id} (Trigger: {trigger_type}, Value: {trigger_value})")
            
            # Execute content-aware banking (priority order)
            if trigger_type == "file_size":
                # File size limit exceeded: Aggressive banking
                target_chars = 3000  # Keep last 3,000 chars (~900 words)
                entries_to_keep = []
                chars_kept = 0
                
                for entry in reversed(conversation['entries']):
                    if chars_kept + len(entry.get('content', '')) <= target_chars:
                        entries_to_keep.insert(0, entry)
                        chars_kept += len(entry.get('content', ''))
                    else:
                        break
                
                entries_to_bank = [e for e in conversation['entries'] if e not in entries_to_keep]
                
            elif trigger_type == "content_length":
                # Bank by character count: Keep last 5,000 chars (~1,500 words)
                target_chars = 5000
                entries_to_keep = []
                chars_kept = 0
                
                for entry in reversed(conversation['entries']):
                    if chars_kept + len(entry.get('content', '')) <= target_chars:
                        entries_to_keep.insert(0, entry)
                        chars_kept += len(entry.get('content', ''))
                    else:
                        break
                
                entries_to_bank = [e for e in conversation['entries'] if e not in entries_to_keep]
                
            else:  # entry_count
                # Bank by entry count: Keep last 50 entries OR if content > 5KB
                entries_to_keep = conversation['entries'][-50:]
                entries_to_bank = conversation['entries'][:-50]
            
            if not entries_to_bank:
                print(f"ℹ️  No entries to bank for conversation {conversation_id}")
                return None
            
            # Generate summary and apply banking
            summary = self._generate_banking_summary(entries_to_bank, trigger_type, trigger_value)
            
            # Create banking summary entry
            banked_summary_entry = {
                "id": f"banked_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "sender": "system",
                "content": summary,
                "timestamp": datetime.now().isoformat(),
                "type": "banked_summary",
                "metadata": {
                    "banking_trigger": trigger_type,
                    "trigger_value": trigger_value,
                    "original_entries_count": len(entries_to_bank),
                    "original_chars_count": sum(len(e.get('content', '')) for e in entries_to_bank),
                    "preserved_entries": len(entries_to_keep),
                    "preserved_chars": sum(len(e.get('content', '')) for e in entries_to_keep)
                }
            }
            
            # Update conversation
            conversation['entries'] = [banked_summary_entry] + entries_to_keep
            conversation['updated_at'] = datetime.now().isoformat()
            
            # Archive original entries
            self._archive_entries(conversation_id, entries_to_bank, trigger_type, trigger_value)
            
            # Log banking operation
            self.add_system_log(f"Banking completed: {len(entries_to_bank)} entries banked, {len(entries_to_keep)} entries preserved (Trigger: {trigger_type})")
            
            print(f"✅ Banking completed: {len(entries_to_bank)} entries → summary + {len(entries_to_keep)} recent entries")
            return summary
            
        except Exception as e:
            print(f"❌ Error during banking: {e}")
            return None
    
    def check_and_bank_conversation(self, conversation_id: str) -> bool:
        """Check if banking is needed and perform it if necessary."""
        try:
            if conversation_id not in self.conversations:
                return False
            
            conversation = self.conversations[conversation_id]
            trigger_type, trigger_value, trigger_secondary = self._calculate_banking_threshold(conversation)
            
            if trigger_type:
                print(f"🏦 Banking threshold exceeded: {trigger_type} = {trigger_value}")
                summary = self.bank_conversation(conversation_id)
                return summary is not None
            else:
                return False
        except Exception as e:
            print(f"⚠️  Error checking banking threshold: {e}")
            return False
    
    def save_state(self, filename: str = "next18hrs_state.yaml"):
        """Save current state to YAML file."""
        # Check if banking is needed before saving
        if self.active_conversation_id:
            self.check_and_bank_conversation(self.active_conversation_id)
        
        state = {
            "conversations": self.conversations,
            "tasks": self.tasks,
            "active_conversation_id": self.active_conversation_id
        }
        with open(filename, 'w') as f:
            yaml.dump(state, f, default_flow_style=False, sort_keys=False)
    
    def load_state(self, filename: str = "next18hrs_state.yaml"):
        """Load state from YAML file."""
        try:
            with open(filename, 'r') as f:
                state = yaml.safe_load(f)
                self.conversations = state.get("conversations", {})
                self.tasks = state.get("tasks", {})
                self.active_conversation_id = state.get("active_conversation_id")
        except FileNotFoundError:
            pass  # Start fresh if no saved state


class CLI:
    """Simple CLI interface for the POC."""
    
    def __init__(self):
        self.engine = Next18HrsPOC()
        self.running = True
    
    def show_help(self):
        """Display available commands."""
        print("\n📋 Next 18hrs POC - Available Commands:")
        print("  dump <text>     - Add thoughts to conversation")
        print("  dump_file <file> - Read and process text from a file")
        print("  tasks           - Show current tasks (Signal view)")
        print("  overview        - Show all tasks")
        print("  conv            - Show current conversation")
        print("  complete <id>   - Mark task as done (e.g., complete 1 or complete task_1)")
        print("  new             - Start new conversation")
        print("  save            - Save current state")
        print("  bank            - Manually trigger banking for current conversation")
        print("  help            - Show this help")
        print("  quit            - Exit")
        print()
    
    def show_tasks(self, view_type: str = "signal"):
        """Display tasks in the specified view."""
        if view_type == "signal":
            tasks = self.engine.get_signal_view()
            print(f"\n🎯 Next 18hrs Signal ({len(tasks)} tasks):")
        else:
            tasks = self.engine.get_overview_view()
            print(f"\n📋 All Tasks ({len(tasks)} total):")
        
        if not tasks:
            print("  No tasks found.")
            return
        
        for task in tasks:
            status = "✓" if task["is_completed"] else "□"
            print(f"  {status} [{task['id']}] P{task['priority']}: {task['description']}")
    
    def show_conversation(self):
        """Display the current conversation."""
        conv = self.engine.get_active_conversation()
        if not conv:
            print("\n❌ No active conversation.")
            return
        
        print(f"\n🗨️  Conversation: {conv['title']}")
        print(f"   Entries: {len(conv['entries'])}")
        print("\n   Recent entries:")
        
        # Show last 5 entries
        recent_entries = conv['entries'][-5:] if len(conv['entries']) > 5 else conv['entries']
        
        for entry in recent_entries:
            timestamp = entry['timestamp'][11:16]  # Extract HH:MM from ISO string
            sender_icon = {"user": "👤", "ai": "🤖", "system": "⚙️"}.get(entry['sender'], "?")
            print(f"   {timestamp} {sender_icon} {entry['content']}")
    
    def process_dump(self, text: str):
        """Process user input through AI."""
        if not text.strip():
            print("❌ Please provide some text to dump.")
            return
        
        # Add user input to conversation
        user_entry = self.engine.add_user_input(text)
        print(f"👤 Added to conversation: {text}")
        
        # Process with AI
        if self.engine.model:
            print("🤖 Processing with AI...")
        else:
            print("🤖 Processing with mock AI...")
            
        result = self.engine.process_with_ai(text)
        
        if "error" in result:
            print(f"❌ AI Error: {result['error']}")
            return
        
        # Show AI response
        print(f"🤖 AI: {result['ai_response']}")
        
        # Show task consolidation or creation results
        if result.get("existing_tasks_updated"):
            print(f"\n{result['message']}")
            print(f"📊 Task update summary:")
            print(f"   • {result['existing_tasks_updated']} existing tasks")
            print(f"   • {result['new_tasks_added']} new tasks added")
            print(f"   • Total tasks: {result['total_tasks']}")
        elif result.get("tasks_created", 0) > 0:
            tasks = result["tasks"]
            print(f"✅ Created {len(tasks)} task(s):")
            for task in tasks:
                print(f"   • {task['description']}")
        else:
            print("ℹ️  No tasks created for this input.")
    
    def process_dump_file(self, filepath: str):
        """Process text from a file through AI."""
        try:
            if not os.path.exists(filepath):
                print(f"❌ File not found: {filepath}")
                return
            
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            
            if not content:
                print("❌ File is empty")
                return
            
            print(f"📁 Reading file: {filepath}")
            print(f"📄 Content length: {len(content)} characters")
            
            # Add file content to conversation
            user_entry = self.engine.add_user_input(f"[File: {filepath}]\n{content}")
            print(f"👤 Added file content to conversation")
            
            # Process with AI
            if self.engine.model:
                print("🤖 Processing with AI...")
            else:
                print("🤖 Processing with mock AI...")
                
            result = self.engine.process_with_ai(content)
            
            if "error" in result:
                print(f"❌ AI Error: {result['error']}")
                return
            
            # Show AI response
            print(f"🤖 AI: {result['ai_response']}")
            
            # Show task consolidation or creation results
            if result.get("tasks_consolidated"):
                print(f"\n{result['message']}")
                print(f"📊 Task consolidation summary:")
                print(f"   • {result['tasks_consolidated']} consolidated tasks")
                print(f"   • {result['new_tasks_added']} new tasks added")
                print(f"   • Total tasks: {result['total_tasks']}")
            elif result.get("tasks_created", 0) > 0:
                tasks = result["tasks"]
                print(f"✅ Created {len(tasks)} task(s):")
                for task in tasks:
                    print(f"   • {task['description']}")
            else:
                print("ℹ️  No tasks created for this input.")
                
        except Exception as e:
            print(f"❌ Error reading file: {str(e)}")
    
    def complete_task(self, task_id: str):
        """Mark a task as completed."""
        if self.engine.complete_task(task_id):
            print(f"✅ Task {task_id} marked as completed!")
        else:
            print(f"❌ Task {task_id} not found.")
    
    def start_new_conversation(self):
        """Start a new conversation."""
        title = input("Enter conversation title (or press Enter for default): ").strip()
        if not title:
            title = "New Conversation"
        
        conv_id = self.engine.create_conversation(title)
        print(f"🆕 New conversation started: {title}")
    
    def save_state(self):
        """Save current state to file."""
        self.engine.save_state()
        print("💾 State saved to next18hrs_state.yaml")
    
    def manual_banking(self, force: bool = False):
        """Manually trigger banking for current conversation."""
        if not self.engine.active_conversation_id:
            print("❌ No active conversation to bank")
            return
        
        conversation = self.engine.conversations[self.engine.active_conversation_id]
        total_entries = len(conversation['entries'])
        total_chars = sum(len(entry.get('content', '')) for entry in conversation['entries'])
        
        print(f"🏦 Manual banking for conversation: {conversation['title']}")
        print(f"   • Entries: {total_entries}")
        print(f"   • Characters: {total_chars}")
        
        # Check if banking is needed
        trigger_type, trigger_value, trigger_secondary = self.engine._calculate_banking_threshold(conversation)
        
        if trigger_type:
            print(f"   • Banking needed: {trigger_type} = {trigger_value}")
            
            if force:
                # Non-interactive mode - proceed automatically
                print("   • Proceeding with banking (force mode)")
                summary = self.engine.bank_conversation(self.engine.active_conversation_id)
                if summary:
                    print("✅ Manual banking completed successfully!")
                else:
                    print("❌ Manual banking failed")
            else:
                # Interactive mode - ask for confirmation
                try:
                    confirm = input("   • Proceed with banking? (y/N): ").strip().lower()
                    if confirm == 'y':
                        summary = self.engine.bank_conversation(self.engine.active_conversation_id)
                        if summary:
                            print("✅ Manual banking completed successfully!")
                        else:
                            print("❌ Manual banking failed")
                    else:
                        print("❌ Banking cancelled")
                except (EOFError, KeyboardInterrupt):
                    print("\n❌ Banking cancelled (input interrupted)")
        else:
            print("   • No banking needed at this time")
            print("   • Current thresholds not exceeded")
    
    def run(self):
        """Main CLI loop."""
        print("🚀 Next 18hrs POC - Task Organizer")
        print("Type 'help' to see available commands")
        
        while self.running:
            try:
                command = input("\n> ").strip().lower()
                
                if command == "quit" or command == "exit":
                    self.running = False
                    print("👋 Goodbye!")
                
                elif command == "help":
                    self.show_help()
                
                elif command == "tasks":
                    self.show_tasks("signal")
                
                elif command == "overview":
                    self.show_tasks("overview")
                
                elif command == "conv":
                    self.show_conversation()
                
                elif command == "new":
                    self.start_new_conversation()
                
                elif command == "save":
                    self.save_state()
                
                elif command == "bank":
                    self.manual_banking()
                
                elif command.startswith("dump_file "):
                    filepath = command[10:]  # Remove "dump_file " prefix
                    self.process_dump_file(filepath)
                
                elif command.startswith("dump "):
                    text = command[5:]  # Remove "dump " prefix
                    self.process_dump(text)
                
                elif command.startswith("complete"):
                    # Handle complete command with flexible formatting
                    parts = command.split()
                    if len(parts) >= 2:
                        task_id = parts[1]
                        # Allow both "1" and "task_1" formats
                        if task_id.isdigit():
                            task_id = f"task_{task_id}"
                        self.complete_task(task_id)
                    else:
                        print("❌ Usage: complete <task_id>")
                        print("   Example: complete task_1 or complete 1")
                
                elif command.startswith("dump"):
                    print("❌ Please provide text after 'dump' command")
                
                elif command:
                    print(f"❌ Unknown command: {command}")
                    print("Type 'help' to see available commands")
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                self.running = False
            except Exception as e:
                print(f"❌ Error: {e}")


def main():
    """Entry point for the POC."""
    # Check for command line arguments
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "dump_file" and len(sys.argv) > 2:
            filepath = sys.argv[2]
            print("🚀 Next 18hrs POC - Processing file...")
            
            # Create engine and process file directly
            engine = Next18HrsPOC()
            cli = CLI()
            cli.engine = engine
            
            # Process the file
            cli.process_dump_file(filepath)
            
            # Show results
            print("\n" + "="*50)
            cli.show_tasks("signal")
            cli.show_tasks("overview")
            
            # Save state
            engine.save_state()
            print("\n💾 State saved to next18hrs_state.yaml")
            return
        
        elif command == "bank":
            print("🚀 Next 18hrs POC - Manual Banking")
            
            # Create engine and perform banking
            engine = Next18HrsPOC()
            cli = CLI()
            cli.engine = engine
            
            # Check if force mode is requested
            force_mode = len(sys.argv) > 2 and sys.argv[2] == "--force"
            
            # Perform banking
            cli.manual_banking(force=force_mode)
            
            # Save state if banking was performed
            if engine.active_conversation_id:
                engine.save_state()
                print("\n💾 State saved to next18hrs_state.yaml")
            return
        
        elif command == "help" or command == "--help" or command == "-h":
            print("🚀 Next 18hrs POC - Task Organizer")
            print("\nUsage:")
            print("  python next18hrs_poc.py                    # Interactive mode")
            print("  python next18hrs_poc.py dump_file <file>   # Process file directly")
            print("  python next18hrs_poc.py bank              # Manual banking (interactive)")
            print("  python next18hrs_poc.py bank --force       # Manual banking (non-interactive)")
            print("  python next18hrs_poc.py help               # Show this help")
            print("\nExamples:")
            print("  python next18hrs_poc.py dump_file simple_dump.txt")
            print("  python next18hrs_poc.py dump_file tasks.txt")
            print("  python next18hrs_poc.py bank --force")
            return
        
        else:
            print(f"❌ Unknown command: {command}")
            print("Use 'python next18hrs_poc.py help' for usage information")
            return
    
    # Default: run interactive CLI
    cli = CLI()
    cli.run()


if __name__ == "__main__":
    main()
